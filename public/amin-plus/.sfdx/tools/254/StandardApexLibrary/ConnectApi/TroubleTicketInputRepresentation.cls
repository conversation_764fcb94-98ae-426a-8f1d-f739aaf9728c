global class TroubleTicketInputRepresentation {
	global List<ConnectApi.TroubleTicketRelatedPartyInputRepresentation> attachment;
	global ConnectApi.TroubleTicketRelatedPartyInputRepresentation channel;
	global String description;
	global String expectedResolutionDate;
	global List<ConnectApi.TroubleTicketRelatedPartyInputRepresentation> externalIdentifier;
	global String name;
	global List<ConnectApi.TroubleTicketRelatedPartyInputRepresentation> note;
	global String priority;
	global List<ConnectApi.TroubleTicketRelatedPartyInputRepresentation> relatedEntity;
	global List<ConnectApi.TroubleTicketRelatedPartyInputRepresentation> relatedParty;
	global String requestedResolutionDate;
	global String severity;
	global String status;
	global String ticketType;
	global List<ConnectApi.TroubleTicketRelatedPartyInputRepresentation> troubleTicketRelationship;
	global TroubleTicketInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
global class TransactionPaymentUpdatesOutputRepresentation {
	global List<ConnectApi.TransactionPaymentUpdatesResponse> details;
	global Integer failures;
	global Integer notProcessed;
	global Integer successes;
	global TransactionPaymentUpdatesOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SmartDataDiscoveryAIModelResidualCollection {
	global String nextPageUrl;
	global List<ConnectApi.SmartDataDiscoveryAIModelResidual> residuals;
	global Integer totalSize;
	global String url;
	global SmartDataDiscoveryAIModelResidualCollection() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
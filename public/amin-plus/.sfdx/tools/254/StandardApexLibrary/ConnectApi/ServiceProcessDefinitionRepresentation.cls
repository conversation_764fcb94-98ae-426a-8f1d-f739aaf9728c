global class ServiceProcessDefinitionRepresentation {
	global List<ConnectApi.AttributeRepresentation> attributes;
	global Map<String,List<ConnectApi.ServiceProcessDependencyDetails>> serviceProcessDefinitionMap;
	global ServiceProcessDefinitionRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class Topic {
	global Datetime createdDate;
	global String description;
	global String id;
	global ConnectApi.TopicImages images;
	global Boolean isBeingDeleted;
	global String name;
	global String nonLocalizedName;
	global Integer talkingAbout;
	global String url;
	global Topic() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
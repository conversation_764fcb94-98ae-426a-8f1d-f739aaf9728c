global class ServiceCatalogItemsFetchOutput {
	global List<ConnectApi.ServiceCatalogItemsOutput> actions;
	global ConnectApi.ServiceCatalogFrequentActionsOutput frequentActions;
	global ConnectApi.EngagementStatusOutput status;
	global ServiceCatalogItemsFetchOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
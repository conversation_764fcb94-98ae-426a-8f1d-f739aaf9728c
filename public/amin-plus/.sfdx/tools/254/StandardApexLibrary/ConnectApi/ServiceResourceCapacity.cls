global class ServiceResourceCapacity {
	global List<ConnectApi.DayCapacity> dayCapacityList;
	global String serviceTerritoryId;
	global String serviceTerritoryName;
	global String workTypeId;
	global String workTypeName;
	global ServiceResourceCapacity() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
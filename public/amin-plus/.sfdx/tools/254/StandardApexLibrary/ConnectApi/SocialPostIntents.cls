global class SocialPostIntents {
	global ConnectApi.ApprovalIntent approvalIntent;
	global ConnectApi.DeleteIntents deleteIntent;
	global ConnectApi.FollowIntents followIntent;
	global ConnectApi.HideSocialPostIntent hideIntent;
	global ConnectApi.LikeIntents likeIntent;
	global ConnectApi.ReplyIntents replyIntent;
	global SocialPostIntents() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
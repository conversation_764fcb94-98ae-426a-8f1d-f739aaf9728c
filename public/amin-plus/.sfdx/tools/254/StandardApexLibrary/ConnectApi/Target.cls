global class Target {
	global ConnectApi.AudienceTarget audience;
	global ConnectApi.FormulaScope formulaScope;
	global String groupName;
	global String id;
	global Integer priority;
	global ConnectApi.PublishStatus publishStatus;
	global List<ConnectApi.Scope> scope;
	global String targetType;
	global String targetValue;
	global String url;
	global Target() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
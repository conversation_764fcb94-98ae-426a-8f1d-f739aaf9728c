global class TextClassificationsResultWithIdOutputRepresentation {
	global String id;
	global ConnectApi.TextClassificationsResultOutputRepresentation result;
	global String status;
	global TextClassificationsResultWithIdOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
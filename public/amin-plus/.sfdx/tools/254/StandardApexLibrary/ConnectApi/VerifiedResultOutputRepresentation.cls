global class VerifiedResultOutputRepresentation {
	global List<ConnectApi.VerifierOutputRepresentation> optionalVerifiers;
	global List<ConnectApi.VerifierOutputRepresentation> requiredVerifiers;
	global VerifiedResultOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
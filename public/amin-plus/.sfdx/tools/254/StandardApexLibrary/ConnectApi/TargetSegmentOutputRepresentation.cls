global class TargetSegmentOutputRepresentation {
	global String adTargetCategorySegmentId;
	global String code;
	global String dataType;
	global String displayType;
	global String name;
	global List<ConnectApi.TargetSegmentValueOutputRepresentation> values;
	global TargetSegmentOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SmartDataDiscoveryProjectedPredictionsCountFromDateIntervalSetting {
	global String dateField;
	global String dateFieldLabel;
	global Integer numIntervals;
	global SmartDataDiscoveryProjectedPredictionsCountFromDateIntervalSetting() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
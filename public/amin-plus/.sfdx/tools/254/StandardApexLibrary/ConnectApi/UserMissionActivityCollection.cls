global class UserMissionActivityCollection {
	global ConnectApi.CommunitySummary community;
	global String userId;
	global List<ConnectApi.AbstractUserMissionActivity> userMissionActivities;
	global String userName;
	global UserMissionActivityCollection() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SmartDataDiscoveryAIModelTransformation {
	global String id;
	global List<ConnectApi.AbstractSmartDataDiscoveryModelField> sourceFields;
	global Map<String,Object> state;
	global List<ConnectApi.AbstractSmartDataDiscoveryModelField> targetFields;
	global ConnectApi.SmartDataDiscoveryAIModelTransformationTypeEnum type;
	global SmartDataDiscoveryAIModelTransformation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
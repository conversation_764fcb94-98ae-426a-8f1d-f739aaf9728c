global class SmartDataDiscoveryClassificationPredictionPropertyInput {
	global ConnectApi.SmartDataDiscoveryClassificationAlgorithmTypeEnum algorithmType;
	global ConnectApi.SmartDataDiscoveryAbstractClassificationThresholdInput classificationThreshold;
	global SmartDataDiscoveryClassificationPredictionPropertyInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
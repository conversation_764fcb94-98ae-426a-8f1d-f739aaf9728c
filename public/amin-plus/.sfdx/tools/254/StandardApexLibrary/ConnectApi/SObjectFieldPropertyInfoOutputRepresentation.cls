global class SObjectFieldPropertyInfoOutputRepresentation {
	global ConnectApi.SObjectFieldDataType dataType;
	global String description;
	global String inlineHelpText;
	global Integer length;
	global Integer precision;
	global Boolean required;
	global Integer scale;
	global SObjectFieldPropertyInfoOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
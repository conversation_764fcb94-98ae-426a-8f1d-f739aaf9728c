global class SurveyMassInvitationEmailInput {
	global Boolean allowGuestUserResponse;
	global Boolean allowParticipantsAccessTheirResponse;
	global String body;
	global Boolean collectAnonymousResponse;
	global String communityId;
	global String emailTemplateId;
	global String fromEmailAddress;
	global Datetime invitationExpirationDate;
	global String invitationOwner;
	global Boolean isPersonalInvitation;
	global String listViewId;
	global String recipientPath;
	global List<String> shareInvitationsWith;
	global String subject;
	global List<String> surveyQuestionIds;
	global String surveySubjectPath;
	global SurveyMassInvitationEmailInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
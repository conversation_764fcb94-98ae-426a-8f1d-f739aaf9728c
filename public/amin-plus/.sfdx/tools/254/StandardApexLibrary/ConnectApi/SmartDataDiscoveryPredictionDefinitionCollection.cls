global class SmartDataDiscoveryPredictionDefinitionCollection {
	global String nextPageUrl;
	global List<ConnectApi.SmartDataDiscoveryPredictionDefinition> predictionDefinitions;
	global Integer totalSize;
	global String url;
	global SmartDataDiscoveryPredictionDefinitionCollection() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
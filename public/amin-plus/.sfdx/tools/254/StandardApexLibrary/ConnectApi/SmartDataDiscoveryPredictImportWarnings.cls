global class SmartDataDiscoveryPredictImportWarnings {
	global List<String> mismatchedColumns;
	global List<String> missingColumns;
	global List<ConnectApi.SmartDataDiscoveryPredictColumn> outOfBoundsColumns;
	global SmartDataDiscoveryPredictImportWarnings() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
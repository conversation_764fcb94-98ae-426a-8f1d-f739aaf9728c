global class RuleLibraryTagMappingsReadOutput {
	global String id;
	global String mappingName;
	global String ruleLibraryApiName;
	global Integer sequenceNumber;
	global String tagName;
	global String usageSubtype;
	global Integer versionNumber;
	global RuleLibraryTagMappingsReadOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
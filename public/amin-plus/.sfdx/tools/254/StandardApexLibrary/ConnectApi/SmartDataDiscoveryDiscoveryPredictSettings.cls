global class SmartDataDiscoveryDiscoveryPredictSettings {
	global List<String> aggregateFunctions;
	global Integer maxMiddleValues;
	global Integer maxPrescriptions;
	global Integer prescriptionImpactPercentage;
	global ConnectApi.SmartDataDiscoveryProjectedPredictionSettingsInput projectedPredictions;
	global SmartDataDiscoveryDiscoveryPredictSettings() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
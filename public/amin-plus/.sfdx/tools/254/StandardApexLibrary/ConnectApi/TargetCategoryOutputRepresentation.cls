global class TargetCategoryOutputRepresentation {
	global String adTargetCategoryId;
	global String code;
	global String name;
	global List<ConnectApi.TargetSegmentOutputRepresentation> segments;
	global TargetCategoryOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SearchBoostBuryRuleOutput {
	global ConnectApi.SearchBoostBuryRuleAction action;
	global Datetime endDate;
	global String id;
	global Integer level;
	global String name;
	global Datetime startDate;
	global ConnectApi.SearchBoostBuryTargetExpressionOutput targetExpression;
	global SearchBoostBuryRuleOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class TroubleTicketRelatedEntityOutputRepresentation {
	global ConnectApi.PartyOrPartyRoleOutputRepresentation partyOrPartyRole;
	global String role;
	global String type;
	global TroubleTicketRelatedEntityOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
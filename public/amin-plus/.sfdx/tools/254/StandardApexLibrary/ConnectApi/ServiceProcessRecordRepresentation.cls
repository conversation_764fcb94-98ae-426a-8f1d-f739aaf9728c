global class ServiceProcessRecordRepresentation {
	global ConnectApi.ServiceCatalogItemAttributes attributes;
	global String caseId;
	global String caseNumber;
	global String svcCatalogItemDefApiName;
	global String svcCatalogRequestId;
	global ServiceProcessRecordRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
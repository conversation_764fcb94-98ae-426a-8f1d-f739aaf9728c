global class SortRule {
	global ConnectApi.CommerceSearchSortRuleDirection direction;
	global String label;
	global ConnectApi.ConnectCommerceSearchSortRuleLabelSuffix labelSuffix;
	global String nameOrId;
	global Integer sortOrder;
	global String sortRuleId;
	global ConnectApi.CommerceSearchSortRuleType type;
	global SortRule() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
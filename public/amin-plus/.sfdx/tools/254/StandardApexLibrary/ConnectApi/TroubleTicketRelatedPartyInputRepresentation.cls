global class TroubleTicketRelatedPartyInputRepresentation {
	global String attachmentType;
	global String description;
	global ConnectApi.TroubleTicketPartyOrPartyRoleInputRepresentation entity;
	global String mimeType;
	global String name;
	global ConnectApi.TroubleTicketPartyOrPartyRoleInputRepresentation partyOrPartyRole;
	global String role;
	global String text;
	global String type;
	global String url;
	global TroubleTicketRelatedPartyInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
global class RuleLibraryVersionInput {
	global String contextDefinitionName;
	global String description;
	global Datetime endDate;
	global String name;
	global String ruleLibraryApiName;
	global Datetime startDate;
	global ConnectApi.RuleLibraryVersionStatusEnumRepresentation status;
	global Integer versionNumber;
	global RuleLibraryVersionInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
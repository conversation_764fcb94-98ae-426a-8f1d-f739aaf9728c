global class VerificationProcessFieldInput {
	global String dataSourceType;
	global String dataType;
	global String developerName;
	global String fieldName;
	global String fieldType;
	global String fieldValueFormula;
	global Boolean isManualInput;
	global String label;
	global VerificationProcessFieldInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
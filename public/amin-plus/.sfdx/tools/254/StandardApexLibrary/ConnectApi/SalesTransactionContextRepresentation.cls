global class SalesTransactionContextRepresentation {
	global ConnectApi.ContextDetails contextDetails;
	global List<ConnectApi.PlaceSalesTransactionErrorResponse> errorResponse;
	global Boolean isSuccess;
	global SalesTransactionContextRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SmartDataDiscoveryRefreshTaskSource {
	global ConnectApi.SmartDataDiscoveryAssetReference datasetVersion;
	global ConnectApi.SmartDataDiscoveryAssetReference story;
	global ConnectApi.SmartDataDiscoveryAssetReference storyVersion;
	global SmartDataDiscoveryRefreshTaskSource() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
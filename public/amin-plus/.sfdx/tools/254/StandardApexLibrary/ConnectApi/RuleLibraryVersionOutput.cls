global class RuleLibraryVersionOutput {
	global String apiName;
	global String contextDefinitionName;
	global String description;
	global Datetime endDate;
	global String id;
	global String name;
	global String ruleLibraryApiName;
	global Datetime startDate;
	global ConnectApi.RuleLibraryVersionStatusEnumRepresentation status;
	global Integer versionNumber;
	global RuleLibraryVersionOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
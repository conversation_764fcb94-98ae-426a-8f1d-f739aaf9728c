global class ReturnOrderItemInputRepresentation {
	global Double quantityReceived;
	global Double quantityRejected;
	global Double quantityReturned;
	global Double quantityToCancel;
	global String reasonForRejection;
	global String returnOrderLineItemId;
	global ReturnOrderItemInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
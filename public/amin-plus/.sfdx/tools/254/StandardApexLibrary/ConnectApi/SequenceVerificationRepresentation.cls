global class SequenceVerificationRepresentation {
	global String developerName;
	global String fieldDataType;
	global String fieldDeveloperName;
	global String fieldType;
	global Boolean finalVerification;
	global Boolean isInput;
	global Boolean isVerified;
	global String label;
	global Integer optionalVerifications;
	global Integer requiredVerifications;
	global Integer retryCount;
	global String selectedRecordId;
	global ConnectApi.ErrorResponseRepresentation status;
	global String verificationToken;
	global SequenceVerificationRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
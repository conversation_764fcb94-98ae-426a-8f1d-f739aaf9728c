global class SmartDataDiscoveryProjectedPredictionsTransformationInput {
	global String assetIdFieldName;
	global String dateFieldName;
	global ConnectApi.SmartDataDiscoveryProjectedPredictionsHistoricalDatasetSourceInput input;
	global Integer numIntervals;
	global ConnectApi.SmartDataDiscoveryProjectedPredictionsIntervalTypeEnum projectedPredictionsIntervalType;
	global String projectionFieldName;
	global Integer seasonalityPeriod;
	global SmartDataDiscoveryProjectedPredictionsTransformationInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
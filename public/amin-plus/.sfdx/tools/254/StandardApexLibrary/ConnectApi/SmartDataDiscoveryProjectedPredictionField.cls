global class SmartDataDiscoveryProjectedPredictionField {
	global ConnectApi.SmartDataDiscoveryProjectedPredictionsIntervalTypeEnum intervalType;
	global String name;
	global Integer numberOfIntervals;
	global List<ConnectApi.SmartDataDiscoveryProjectedValue> projectedValues;
	global SmartDataDiscoveryProjectedPredictionField() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
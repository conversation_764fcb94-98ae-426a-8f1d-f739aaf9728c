global class ShiftsFromPatternInput {
	global String schedulingEndDate;
	global Integer schedulingOccurrences;
	global String schedulingStartDate;
	global String serviceResourceId;
	global List<String> serviceResourceIdList;
	global String serviceTerritoryId;
	global String shiftStatus;
	global ShiftsFromPatternInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
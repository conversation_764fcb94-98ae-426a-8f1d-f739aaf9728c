global class WaitlistAnalytics {
	global Integer avgParticipants;
	global Integer avgWaitingTime;
	global Integer currentParticipant;
	global Integer servedParticipant;
	global Integer totalParticipant;
	global Integer totalWaitingTime;
	global WaitlistAnalytics() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
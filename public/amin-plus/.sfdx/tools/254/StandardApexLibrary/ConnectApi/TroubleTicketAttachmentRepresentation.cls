global class TroubleTicketAttachmentRepresentation {
	global String attachmentType;
	global String description;
	global String id;
	global String mimeType;
	global String name;
	global String type;
	global String url;
	global TroubleTicketAttachmentRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
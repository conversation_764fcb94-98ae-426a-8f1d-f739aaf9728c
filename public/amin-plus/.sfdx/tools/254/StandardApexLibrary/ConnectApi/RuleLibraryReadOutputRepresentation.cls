global class RuleLibraryReadOutputRepresentation {
	global String apiName;
	global String id;
	global String name;
	global List<Integer> ruleLibraryVersionNumbers;
	global String usageType;
	global RuleLibraryReadOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SmartDataDiscoveryFieldMappingAnalyticsDatasetField {
	global String sobjectFieldJoinKey;
	global ConnectApi.SmartDataDiscoveryAssetReference source;
	global String sourceFieldJoinKey;
	global SmartDataDiscoveryFieldMappingAnalyticsDatasetField() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
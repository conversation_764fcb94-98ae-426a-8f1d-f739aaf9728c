global class SmartDataDiscoveryPredictColumn {
	global String columnLabel;
	global String columnName;
	global String columnValue;
	global ConnectApi.SmartDataDiscoveryPredictColumnCustomText customText;
	global String inputValue;
	global SmartDataDiscoveryPredictColumn() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class UiObjectRepresentationCardsGetResult {
	global List<ConnectApi.CardOutput> cards;
	global String currentConfigName;
	global Integer currentConfigOffset;
	global String errorMessage;
	global Boolean hasMoreData;
	global Boolean isSuccess;
	global UiObjectRepresentationCardsGetResult() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SmartDataDiscoveryFieldMappingAnalyticsDatasetFieldInput {
	global String sobjectFieldJoinKey;
	global ConnectApi.SmartDataDiscoveryAssetReferenceInput source;
	global String sourceFieldJoinKey;
	global SmartDataDiscoveryFieldMappingAnalyticsDatasetFieldInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
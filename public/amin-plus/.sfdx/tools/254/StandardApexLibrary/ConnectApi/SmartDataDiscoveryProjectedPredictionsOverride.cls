global class SmartDataDiscoveryProjectedPredictionsOverride {
	global String assetIdField;
	global ConnectApi.AbstractSmartDataDiscoveryProjectedPredictionsIntervalSetting intervalOverride;
	global SmartDataDiscoveryProjectedPredictionsOverride() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SmartDataDiscoveryAbstractPredict {
	global Double baseLine;
	global ConnectApi.SmartDataDiscoveryPredictImportWarnings importWarnings;
	global List<ConnectApi.SmartDataDiscoveryPredictCondition> middleValues;
	global Double other;
	global Integer smallTermCount;
	global Double total;
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
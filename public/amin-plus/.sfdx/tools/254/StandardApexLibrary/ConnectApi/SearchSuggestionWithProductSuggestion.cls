global class SearchSuggestionWithProductSuggestion {
	global List<ConnectApi.ProductSummary> suggestedProducts;
	global SearchSuggestionWithProductSuggestion() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global List<ConnectApi.ProductSummary> getSuggestedProducts() { }
	global String getValue() { }
	global Integer hashCode() { }
	global void setSuggestedProducts(List<ConnectApi.ProductSummary> value) { }
	global void setValue(String value) { }
	global String toString() { }

}
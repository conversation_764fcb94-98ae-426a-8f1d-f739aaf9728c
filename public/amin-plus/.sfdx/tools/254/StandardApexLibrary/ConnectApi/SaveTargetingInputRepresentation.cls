global class SaveTargetingInputRepresentation {
	global List<ConnectApi.SaveTargetEntityDataInputRepresentation> entityData;
	global ConnectApi.SaveTargetExpressionInputRepresentation expression;
	global ConnectApi.SaveTargetDataInputRepresentation targetingData;
	global SaveTargetingInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
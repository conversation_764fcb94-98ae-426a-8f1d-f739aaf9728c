global class SmartDataDiscoveryFieldMapping {
	global ConnectApi.AbstractSmartDataDiscoveryFieldMappingSource input;
	global ConnectApi.SmartDataDiscoveryFieldMappingMappedField mappedField;
	global ConnectApi.AbstractSmartDataDiscoveryModelField modelField;
	global SmartDataDiscoveryFieldMapping() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
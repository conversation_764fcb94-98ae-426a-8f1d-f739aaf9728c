global class SmartDataDiscoveryLiveMetricsRepresentation {
	global List<ConnectApi.SmartDataDiscoveryLiveMetricDetailRepresentation> disparateImpacts;
	global List<ConnectApi.SmartDataDiscoveryLiveMetricDetailRepresentation> missingColumns;
	global List<ConnectApi.SmartDataDiscoveryLiveMetricDetailRepresentation> outOfBoundsColumns;
	global List<ConnectApi.SmartDataDiscoveryLiveMetricDetailRepresentation> predictions;
	global List<ConnectApi.SmartDataDiscoveryLiveMetricDetailRepresentation> warnings;
	global SmartDataDiscoveryLiveMetricsRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
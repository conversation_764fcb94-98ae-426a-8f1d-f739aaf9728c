global class SmartDataDiscoveryAIModelTransformationInput {
	global List<String> sourceFieldNames;
	global Object state;
	global List<String> targetFieldNames;
	global ConnectApi.SmartDataDiscoveryAIModelTransformationTypeEnum type;
	global SmartDataDiscoveryAIModelTransformationInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
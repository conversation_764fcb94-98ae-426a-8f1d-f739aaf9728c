global class WaitlistCheckInInput {
	global String checkInPageURL;
	global String confirmationEmailTemplate;
	global String description;
	global List<ConnectApi.ExtendedFieldInput> extendedFields;
	global ConnectApi.LeadInput lead;
	global String participantId;
	global String serviceResourceId;
	global String waitlistId;
	global String workTypeGroupId;
	global String workTypeId;
	global WaitlistCheckInInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
global class ServiceTerritoryCapacity {
	global List<ConnectApi.DayCapacity> dayCapacityList;
	global String serviceResourceId;
	global String serviceResourceName;
	global String workTypeId;
	global String workTypeName;
	global ServiceTerritoryCapacity() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
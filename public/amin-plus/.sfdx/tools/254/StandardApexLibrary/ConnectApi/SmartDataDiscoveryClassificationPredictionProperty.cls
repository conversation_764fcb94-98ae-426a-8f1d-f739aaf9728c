global class SmartDataDiscoveryClassificationPredictionProperty {
	global ConnectApi.SmartDataDiscoveryClassificationAlgorithmTypeEnum algorithmType;
	global ConnectApi.SmartDataDiscoveryAbstractClassificationThreshold classificationThreshold;
	global SmartDataDiscoveryClassificationPredictionProperty() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
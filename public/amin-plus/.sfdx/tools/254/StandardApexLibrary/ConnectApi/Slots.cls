global class Slots {
	global String additionalInformation;
	global String appointmentType;
	global String city;
	global String comments;
	global String contactId;
	global String country;
	global String description;
	global String postalCode;
	global String schedEndTime;
	global String schedStartTime;
	global String serviceAppointmentId;
	global String serviceResourceId;
	global String serviceTerritoryId;
	global String state;
	global String status;
	global String street;
	global String subject;
	global String workTypeId;
	global Slots() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
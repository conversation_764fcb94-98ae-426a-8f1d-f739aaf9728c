global class SegmentIntelligenceSetupConfigDataResponse {
	global String createdById;
	global String createdDate;
	global String dataSpace;
	global String developerName;
	global String key;
	global String lastModifiedById;
	global String lastModifiedByName;
	global String lastModifiedDate;
	global String masterLabel;
	global String recordId;
	global String value;
	global SegmentIntelligenceSetupConfigDataResponse() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
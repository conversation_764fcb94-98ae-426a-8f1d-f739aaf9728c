global class ScheduledRecommendation {
	global ConnectApi.RecommendationChannel channel;
	global Boolean enabled;
	global String id;
	global Integer rank;
	global String recommendationAudienceId;
	global ConnectApi.RecommendationDefinition recommendationDefinitionRepresentation;
	global String url;
	global ScheduledRecommendation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SearchOutputRepresentation {
	global Boolean isSuccess;
	global String message;
	global List<ConnectApi.SearchResultRepresentation> searchResult;
	global List<ConnectApi.SearchResultHeaderRepresentation> searchResultHeader;
	global SearchOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
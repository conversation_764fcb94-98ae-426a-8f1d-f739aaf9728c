global class SObjectFieldOutputRepresentation {
	global ConnectApi.SObjectFieldInfoOutputRepresentation fieldInfo;
	global ConnectApi.SObjectFieldPropertyInfoOutputRepresentation fieldProperties;
	global SObjectFieldOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class SocialCustomerServiceProviderSettings {
	global List<ConnectApi.SocialCustomerServiceMessageTypeSettings> messageTypeSettings;
	global ConnectApi.SocialNetworkProvider provider;
	global SocialCustomerServiceProviderSettings() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
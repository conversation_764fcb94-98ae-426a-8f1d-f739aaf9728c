global class SmartDataDiscoveryProjectedPredictionsTransformation {
	global String assetIdFieldName;
	global String dateFieldName;
	global ConnectApi.SmartDataDiscoveryProjectedPredictionsHistoricalDatasetInput input;
	global Integer numIntervals;
	global ConnectApi.SmartDataDiscoveryProjectedPredictionsIntervalTypeEnum projectedPredictionsIntervalType;
	global String projectionFieldName;
	global Integer seasonalityPeriod;
	global SmartDataDiscoveryProjectedPredictionsTransformation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
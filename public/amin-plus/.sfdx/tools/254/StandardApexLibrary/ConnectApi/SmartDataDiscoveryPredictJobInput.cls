global class SmartDataDiscoveryPredictJobInput {
	global ConnectApi.SmartDataDiscoveryComplexFilterInput filters;
	global String label;
	global ConnectApi.SmartDataDiscoveryAssetReferenceInput predictionDefinition;
	global Boolean useTerminalStateFilter;
	global SmartDataDiscoveryPredictJobInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
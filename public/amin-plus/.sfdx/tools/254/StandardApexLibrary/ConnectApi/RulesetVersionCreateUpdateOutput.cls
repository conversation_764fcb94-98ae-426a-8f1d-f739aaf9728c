global class RulesetVersionCreateUpdateOutput {
	global String id;
	global List<ConnectApi.RulesetVersionCreateUpdateErrorDetail> rulesetVersionCreateUpdateErrorDetails;
	global Boolean success;
	global RulesetVersionCreateUpdateOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
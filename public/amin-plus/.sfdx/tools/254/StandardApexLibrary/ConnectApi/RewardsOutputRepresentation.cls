global class RewardsOutputRepresentation {
	global List<ConnectApi.BadgeRewardsOutputRepresentation> badgeReward;
	global List<ConnectApi.PointsRewardsOutputRepresentation> pointsRewards;
	global List<ConnectApi.VoucherRewardsOutputRepresentation> voucherRewards;
	global RewardsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
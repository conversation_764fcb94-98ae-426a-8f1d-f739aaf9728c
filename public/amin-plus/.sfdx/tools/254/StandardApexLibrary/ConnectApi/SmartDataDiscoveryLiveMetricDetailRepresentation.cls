global class SmartDataDiscoveryLiveMetricDetailRepresentation {
	global Datetime endDate;
	global Integer rowCount;
	global Datetime startDate;
	global Map<String,Object> value;
	global SmartDataDiscoveryLiveMetricDetailRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
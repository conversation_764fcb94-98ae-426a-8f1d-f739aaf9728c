global class SearchFacet {
	global ConnectApi.CommerceSearchAttributeType attributeType;
	global String displayName;
	global Integer displayRank;
	global ConnectApi.CommerceSearchFacetDisplayType displayType;
	global ConnectApi.CommerceSearchFacetType facetType;
	global String nameOrId;
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
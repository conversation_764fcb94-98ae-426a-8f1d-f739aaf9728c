global class ServiceProcessRequestInputRepresentation {
	global Map<String,ConnectApi.GenericObject> attributes;
	global Map<String,ConnectApi.GenericObject> caseInfo;
	global List<ConnectApi.documentInfoAttributes> documentInfo;
	global String svcCatalogItemDefApiName;
	global ServiceProcessRequestInputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}
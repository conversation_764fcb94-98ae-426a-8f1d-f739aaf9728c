global class SmartDataDiscoveryModelCard {
	global ConnectApi.SmartDataDiscoveryContact contact;
	global ConnectApi.SmartDataDiscoveryUser createdBy;
	global Datetime createdDate;
	global String id;
	global String label;
	global ConnectApi.SmartDataDiscoveryUser lastModifiedBy;
	global Datetime lastModifiedDate;
	global Map<String,Object> sections;
	global String url;
	global SmartDataDiscoveryModelCard() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
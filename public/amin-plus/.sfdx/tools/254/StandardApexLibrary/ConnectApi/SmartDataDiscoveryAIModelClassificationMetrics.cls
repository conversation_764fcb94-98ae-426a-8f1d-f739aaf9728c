global class SmartDataDiscoveryAIModelClassificationMetrics {
	global Double auc;
	global Double falseNegativeRate;
	global Double falsePositiveRate;
	global Double gini;
	global Double mcc;
	global Double trueNegativeRate;
	global Double truePositiveRate;
	global SmartDataDiscoveryAIModelClassificationMetrics() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
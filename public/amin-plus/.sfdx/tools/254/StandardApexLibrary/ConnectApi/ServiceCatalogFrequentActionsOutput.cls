global class ServiceCatalogFrequentActionsOutput {
	global List<ConnectApi.ServiceCatalogStaticActionsOutput> dynamicActions;
	global List<ConnectApi.ServiceCatalogStaticActionsOutput> staticActions;
	global ServiceCatalogFrequentActionsOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
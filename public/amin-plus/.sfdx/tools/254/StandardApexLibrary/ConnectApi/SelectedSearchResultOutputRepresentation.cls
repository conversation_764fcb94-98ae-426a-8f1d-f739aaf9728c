global class SelectedSearchResultOutputRepresentation {
	global String objectName;
	global String selectedRecordId;
	global List<ConnectApi.FieldValueOutputRepresentation> selectedRecordObject;
	global SelectedSearchResultOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
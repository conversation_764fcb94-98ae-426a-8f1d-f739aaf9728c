global class SiteSearchItem {
	global String contentReference;
	global String contentTypeDeveloperName;
	global String highlightedSnippet;
	global String id;
	global ConnectApi.ConnectSitesPageType pageType;
	global String title;
	global SiteSearchItem() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
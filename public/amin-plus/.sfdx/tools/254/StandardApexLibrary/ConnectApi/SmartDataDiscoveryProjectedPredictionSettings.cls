global class SmartDataDiscoveryProjectedPredictionSettings {
	global Integer confidenceInterval;
	global Integer numberOfIntervalsToProjectAhead;
	global Boolean showProjectedPredictionByInterval;
	global SmartDataDiscoveryProjectedPredictionSettings() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}
global class AccountSettingsController {
	global Object clone() { }
	global static String getCity() { }
	global static String getCountry() { }
	global static List<Map<String,Object>> getExtraFields(String extraFieldsFieldSet) { }
	global static String getFirstName() { }
	global static String getLanguage() { }
	global static String getLastName() { }
	global static String getLocale() { }
	global static String getMobilePhone() { }
	global static String getPostalCode() { }
	global static String getState() { }
	global static String getStreet() { }
	global static String getTimeZone() { }
	global static String getUserEmail() { }
	global static String getWorkPhone() { }

}
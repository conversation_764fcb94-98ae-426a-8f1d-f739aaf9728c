global class ProcessSubmitRequest {
	global String objectid;
	global String processdefinitionnameorid;
	global Boolean skipentrycriteria;
	global String submitterid;
	global ProcessSubmitRequest() { }
	global Boolean equals(Object obj) { }
	global String getComments() { }
	global List<Id> getNextApproverIds() { }
	global String getObjectId() { }
	global String getProcessDefinitionNameOrId() { }
	global Boolean getSkipEntryCriteria() { }
	global String getSubmitterId() { }
	global Integer hashCode() { }
	global void setComments(String param0) { }
	global void setNextApproverIds(List<Id> param0) { }
	global void setObjectId(String param0) { }
	global void setProcessDefinitionNameOrId(String param0) { }
	global void setSkipEntryCriteria(Boolean param0) { }
	global void setSubmitterId(String param0) { }
	global String toString() { }

}
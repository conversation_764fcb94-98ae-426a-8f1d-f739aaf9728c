global class PaymentMethodTokenizationResponse {
	global PaymentMethodTokenizationResponse() { }
	global Object clone() { }
	global void setGatewayAvsCode(String gatewayAvsCode) { }
	global void setGatewayDate(Datetime gatewayDate) { }
	global void setGatewayMessage(String gatewayMessage) { }
	global void setGatewayResultCode(String gatewayResultCode) { }
	global void setGatewayResultCodeDescription(String gatewayResultCodeDescription) { }
	global void setGatewayToken(String gatewayToken) { }
	global void setGatewayTokenDetails(String gatewayTokenDetails) { }
	global void setGatewayTokenEncrypted(String gatewayTokenEncrypted) { }
	global void setSalesforceResultCodeInfo(commercepayments.SalesforceResultCodeInfo salesforceResultCodeInfo) { }

}
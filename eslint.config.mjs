import globals from "globals";
import pluginReact from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import prettier from "eslint-plugin-prettier";
import nextPlugin from "@next/eslint-plugin-next";
import importPlugin from "eslint-plugin-import";
import a11y from "eslint-plugin-jsx-a11y";

export default [
  {
    files: ["**/*.{js,mjs,cjs,jsx,ts,tsx}"],
    ignores: [
      "node_modules/**",
      ".next/**",
      "dist/**",
      "build/**",
      "public/**",
      "coverage/**",
      "*.config.{js,ts}"
    ],
    languageOptions: {
      ecmaVersion: 2024,
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: { jsx: true }
      },
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2021,
        React: true,
        JSX: true,
        NodeJS: true,
        // إضافة متغيرات Tauri/Electron
        __TAURI__: "readonly",
        global: "writable"
      }
    },
    plugins: {
      react: pluginReact,
      "react-hooks": reactHooks,
      prettier: prettier,
      next: nextPlugin,
      import: importPlugin,
      "jsx-a11y": a11y
    },
    settings: {
      react: { version: "detect" },
      next: { rootDir: "." },
      "import/resolver": {
        typescript: {
          alwaysTryTypes: true,
          project: "./tsconfig.json",
        }
      }
    },
    rules: {
      // قواعد Prettier
      "prettier/prettier": ["error", {
        singleQuote: true,
        semi: false,
        trailingComma: "all",
        tabWidth: 2,
        printWidth: 100,
        endOfLine: "auto"
      }],
      
      // قواعد TypeScript
      "no-unused-vars": ["warn", {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_"
      }],
      
      // قواعد React
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/no-unescaped-entities": "off",
      "react/display-name": "off",
      
      // قواعد React Hooks
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "warn",
      
      // قواعد Next.js
      "next/no-html-link-for-pages": "error",
      
      // قواعد Import
      "import/order": ["warn", {
        "groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
        "newlines-between": "always",
        "alphabetize": { "order": "asc", "caseInsensitive": true }
      }],
      
      // قواعد الوصول (Accessibility)
      "jsx-a11y/alt-text": "warn",
      "jsx-a11y/aria-props": "warn",
      
      // قواعد عامة
      "no-console": ["warn", { "allow": ["warn", "error"] }],
      "prefer-const": "warn",
      "eqeqeq": ["error", "always"]
    }
  },
  // تكوين منفصل لملفات التكوين
  {
    files: ["*.config.{js,ts}", "next.config.{js,mjs}", "tailwind.config.{js,ts}"],
    languageOptions: {
      globals: {
        ...globals.node
      }
    },
    rules: {
      "@typescript-eslint/no-var-requires": "off"
    }
  }
];
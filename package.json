{"name": "amin-plus", "version": "1.0.0", "private": true, "main": "electron/main.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "node --no-warnings ./node_modules/.bin/jest --passWithNoTests --detectOpenHandles", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:seed": "node -r ts-node/register prisma/seed.js", "db:reset": "npx prisma migrate reset --force", "db:backup": "./scripts/backup-db.sh", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3004 && electron .\"", "electron:build": "next build && electron-builder", "electron:start": "electron .", "electron:pack": "electron-builder --dir", "electron:dist": "electron-builder", "electron:mac": "electron-builder --mac", "electron:win": "electron-builder --win", "electron:linux": "electron-builder --linux", "electron:all": "electron-builder -mwl", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:info": "tauri info"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@prisma/client": "^6.6.0", "@radix-ui/react-checkbox": "1.2.3", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-popover": "1.1.11", "@radix-ui/react-select": "2.2.2", "@radix-ui/react-separator": "1.1.4", "@radix-ui/react-slider": "1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@tauri-apps/api": "2.5.0", "@types/qrcode": "1.5.5", "bcrypt": "5.1.1", "bcryptjs": "3.0.2", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "cookies-next": "5.1.0", "date-fns": "4.1.0", "html-to-pdf": "0.1.11", "html2canvas": "1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "3.0.1", "lucide-react": "0.503.0", "next": "^15.2.5", "next-auth": "^4.24.11", "qrcode": "1.5.4", "react": "^19.1.0", "react-day-picker": "9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.48.2", "react-hotkeys-hook": "5.0.1", "recharts": "^2.15.3", "tailwind-merge": "3.2.0", "zod": "3.25.67"}, "devDependencies": {"@jest/globals": "^29.7.0", "@tauri-apps/cli": "2.5.0", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.9", "@types/node": "22.15.0", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@types/testing-library__jest-dom": "^6.0.0", "autoprefixer": "10.4.21", "concurrently": "^8.2.2", "electron": "35.2.1", "electron-builder": "26.0.12", "eslint": "9.25.1", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "29.7.0", "next-themes": "0.4.6", "postcss": "8.5.3", "prisma": "^6.6.0", "sonner": "2.0.3", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.8.3", "wait-on": "^7.2.0"}, "build": {"appId": "com.aminplus.app", "productName": "<PERSON>in Plus | أمين بلس", "copyright": "Copyright © 2023-2024 Amin Plus | أمين بلس", "files": ["electron/**/*", "out/**/*", "package.json"], "directories": {"output": "dist", "buildResources": "public"}, "mac": {"category": "public.app-category.business", "icon": "public/logo.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "electron/entitlements.mac.plist", "entitlementsInherit": "electron/entitlements.mac.plist", "target": ["dmg", "zip"]}, "win": {"icon": "public/logo.ico", "target": ["nsis", "portable"]}, "linux": {"icon": "public/logo.png", "target": ["AppImage", "deb", "rpm"], "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON>in Plus | أمين بلس"}, "publish": {"provider": "github", "releaseType": "release"}}}
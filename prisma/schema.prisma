// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite" // SQLite للتطوير، PostgreSQL للإنتاج
  url      = env("DATABASE_URL")
}

model Permission {
  id             Int              @id @default(autoincrement())
  permission     String           @unique
  description    String?
  rolePermissions RolePermission[]
}

model Role {
  id             Int              @id @default(autoincrement())
  name           String           @unique
  description    String?
  users          User[]
  rolePermissions RolePermission[]
}

model RolePermission {
  id             Int              @id @default(autoincrement())
  roleId         Int
  permissionId   Int
  role           Role             @relation(fields: [roleId], references: [id])
  permission     Permission       @relation(fields: [permissionId], references: [id])

  @@unique([roleId, permissionId])
}

model User {
  id           Int           @id @default(autoincrement())
  email        String        @unique
  name         String
  password     String
  isActive     Boolean       @default(true)
  roleId       Int
  role         Role          @relation(fields: [roleId], references: [id])
  securityLogs SecurityLog[] // سجلات الأمان للمستخدم
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  @@index([email])
  @@index([isActive])
  @@index([roleId])
  @@index([createdAt])
}

model Company {
  id          Int       @id @default(autoincrement())
  name        String
  nameEn      String?
  logo        String?
  address     String?
  city        String?
  country     String?   @default("الإمارات العربية المتحدة")
  phone       String?
  email       String?
  taxNumber   String?   // رقم التسجيل الضريبي
  website     String?
  bankDetails String?
  currency    String?   @default("AED")
  taxRate     Float?    @default(5)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  invoices    Invoice[]
  expenses    Expense[]
  products    Product[]
}

model Customer {
  id            Int       @id @default(autoincrement())
  name          String    // الاسم بالعربية
  nameEn        String?   // الاسم بالإنجليزية
  email         String?
  phone         String?
  address       String?
  city          String?
  country       String?   @default("الإمارات العربية المتحدة")
  taxNumber     String?   // الرقم الضريبي
  contactPerson String?   // الشخص المسؤول
  contactPhone  String?   // هاتف الشخص المسؤول
  contactEmail  String?   // بريد الشخص المسؤول
  notes         String?
  customerType  String?   @default("company") // نوع العميل (individual, company, distributor)
  status        String    @default("active") // الحالة (active, inactive)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  invoices      Invoice[]

  @@index([email])
  @@index([phone])
  @@index([status])
  @@index([customerType])
}

model ProductCategory {
  id          Int       @id @default(autoincrement())
  name        String
  nameEn      String?
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Product {
  id              Int             @id @default(autoincrement())
  name            String          // الاسم بالعربية
  nameEn          String?         // الاسم بالإنجليزية
  description     String?         // الوصف بالعربية
  descriptionEn   String?         // الوصف بالإنجليزية
  sku             String?         // رمز المنتج
  barcode         String?         // الباركود
  price           Float           // سعر البيع
  cost            Float?          @default(0) // التكلفة
  wholesalePrice  Float?          // سعر الجملة
  category        String?         // الفئة بالعربية
  categoryEn      String?         // الفئة بالإنجليزية
  unit            String?         @default("قطعة") // الوحدة بالعربية
  unitEn          String?         @default("Unit") // الوحدة بالإنجليزية
  taxRate         Float           @default(5) // معدل الضريبة
  status          String          @default("active") // الحالة (active, inactive)

  // إدارة المخزون
  trackInventory  Boolean         @default(false) // تتبع المخزون
  currentStock    Int             @default(0) // المخزون الحالي
  minStock        Int             @default(0) // الحد الأدنى
  maxStock        Int             @default(0) // الحد الأقصى

  // معلومات إضافية
  isComposite     Boolean         @default(false) // هل المنتج مركب
  imageUrl        String?         // صورة المنتج

  // العلاقات
  productCategory ProductCategory? @relation(fields: [categoryId], references: [id])
  categoryId      Int?
  company         Company?        @relation(fields: [companyId], references: [id])
  companyId       Int?

  // التواريخ
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // العلاقات مع الجداول الأخرى
  invoiceItems    InvoiceItem[]
  recipeItems     RecipeItem[]    // المقادير التي يتكون منها المنتج
  ingredientIn    RecipeItem[]    @relation("IngredientProduct") // المنتجات التي يستخدم فيها كمكون
  productionPlans ProductionPlan[] // خطط الإنتاج للمنتج
  planDetails     ProductionPlanDetail[] // تفاصيل خطط الإنتاج التي يستخدم فيها كمكون
  wastage         ProductionWastage[] // هدر الإنتاج للمكون
  purchaseItems   PurchaseRequestItem[] // عناصر طلبات الشراء

  @@index([sku])
  @@index([barcode])
  @@index([status])
  @@index([category])
  @@index([trackInventory])
  @@index([currentStock])
}

model Invoice {
  id              Int           @id @default(autoincrement())
  number          String        @unique // رقم الفاتورة
  customer        Customer      @relation(fields: [customerId], references: [id])
  customerId      Int
  company         Company?      @relation(fields: [companyId], references: [id])
  companyId       Int?
  issueDate       String        // تاريخ الإصدار
  dueDate         String        // تاريخ الاستحقاق
  status          String        @default("draft") // الحالة (draft, pending, sent, paid, overdue, cancelled)

  // المبالغ
  subtotal        Float         @default(0) // المجموع الفرعي
  taxAmount       Float         @default(0) // مبلغ الضريبة
  discountAmount  Float         @default(0) // مبلغ الخصم
  total           Float         @default(0) // الإجمالي
  currency        String        @default("AED") // العملة

  // معلومات إضافية
  notes           String?       // ملاحظات
  terms           String?       // الشروط والأحكام

  // معلومات الدفع
  paymentStatus   String        @default("pending") // حالة الدفع (pending, paid, partial, cancelled)
  paymentMethod   String?       // طريقة الدفع (cash, bank_transfer, credit_card)
  paidAt          DateTime?     // تاريخ الدفع

  // التواريخ
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // العلاقات
  items           InvoiceItem[]
  payments        Payment[]

  @@index([number])
  @@index([status])
  @@index([paymentStatus])
  @@index([customerId])
  @@index([issueDate])
  @@index([dueDate])
}

model InvoiceItem {
  id            Int      @id @default(autoincrement())
  invoice       Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId     Int
  product       Product? @relation(fields: [productId], references: [id])
  productId     Int?

  // معلومات العنصر
  name          String   // اسم العنصر
  nameEn        String?  // اسم العنصر بالإنجليزية
  description   String?  // وصف العنصر
  quantity      Float    // الكمية
  unitPrice     Float    // سعر الوحدة
  taxRate       Float    @default(5) // معدل الضريبة
  taxAmount     Float    @default(0) // مبلغ الضريبة
  total         Float    @default(0) // الإجمالي

  // معلومات إضافية
  notes         String?  // ملاحظات

  // التواريخ
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([invoiceId])
  @@index([productId])
}

model Payment {
  id            Int      @id @default(autoincrement())
  invoice       Invoice  @relation(fields: [invoiceId], references: [id])
  invoiceId     Int
  amount        Float
  paymentMethod String
  paymentDate   DateTime @default(now())
  reference     String?  // Payment reference such as bank transaction number
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([invoiceId])
}

// نموذج فئة المصروفات
// Expense Category Model
model ExpenseCategory {
  id          Int       @id @default(autoincrement())
  name        String    // اسم الفئة (مثل: إيجار، رواتب، مشتريات، مرافق)
  description String?   // وصف الفئة
  icon        String?   // أيقونة الفئة
  color       String?   // لون الفئة للعرض في الواجهة
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  expenses    Expense[] // المصروفات المرتبطة بهذه الفئة

  @@index([name])
}

// نموذج المصروفات المحسن
// Enhanced Expense Model
model Expense {
  id               Int              @id @default(autoincrement())
  title            String           // عنوان المصروف
  description      String           // وصف المصروف
  amount           Float            // مبلغ المصروف
  taxAmount        Float?           // مبلغ الضريبة
  totalAmount      Float            // المبلغ الإجمالي (المبلغ + الضريبة)
  receiptUrl       String?          // رابط صورة الإيصال
  expenseDate      DateTime         @default(now()) // تاريخ المصروف
  dueDate          DateTime?        // تاريخ الاستحقاق (للمصروفات المتكررة أو المؤجلة)
  paymentStatus    String           @default("PAID") // حالة الدفع (PAID, PENDING, PARTIAL, CANCELLED)
  paymentMethod    String?          // طريقة الدفع (CASH, BANK_TRANSFER, CREDIT_CARD)
  reference        String?          // رقم مرجعي للمصروف
  recurring        Boolean          @default(false) // هل المصروف متكرر
  recurringPeriod  String?          // فترة التكرار (DAILY, WEEKLY, MONTHLY, QUARTERLY, YEARLY)
  recurringEndDate DateTime?        // تاريخ انتهاء التكرار
  category         ExpenseCategory? @relation(fields: [categoryId], references: [id])
  categoryId       Int?
  supplier         Supplier?        @relation(fields: [supplierId], references: [id])
  supplierId       Int?
  company          Company?         @relation(fields: [companyId], references: [id])
  companyId        Int?
  tags             ExpenseTag[]     // العلامات المرتبطة بالمصروف
  attachments      Attachment[]     // المرفقات المرتبطة بالمصروف
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  @@index([categoryId])
  @@index([supplierId])
  @@index([paymentStatus])
  @@index([expenseDate])
  @@index([recurring])
  @@index([recurringPeriod])
}

// نموذج علامات المصروفات
// Expense Tags Model
model ExpenseTag {
  id        Int       @id @default(autoincrement())
  name      String    @unique // اسم العلامة
  color     String?   // لون العلامة
  expenses  Expense[] // المصروفات المرتبطة بهذه العلامة
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([name])
}

// نموذج المرفقات
// Attachments Model
model Attachment {
  id          Int      @id @default(autoincrement())
  fileName    String   // اسم الملف
  fileUrl     String   // رابط الملف
  fileType    String   // نوع الملف (PDF, IMAGE, DOCUMENT)
  fileSize    Int?     // حجم الملف بالبايت
  expense     Expense? @relation(fields: [expenseId], references: [id])
  expenseId   Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([expenseId])
  @@index([fileType])
}

model Supplier {
  id        Int       @id @default(autoincrement())
  name      String
  email     String?
  phone     String?
  address   String?
  notes     String?
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  purchases Purchase[]
  expenses  Expense[] // المصروفات المرتبطة بهذا المورد
}

model Purchase {
  id            Int           @id @default(autoincrement())
  supplier      Supplier      @relation(fields: [supplierId], references: [id])
  supplierId    Int
  purchaseDate  DateTime      @default(now())
  total         Float
  status        String        @default("RECEIVED") // ORDERED, PARTIAL, RECEIVED
  reference     String?       // Purchase reference number
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  items         PurchaseItem[]

  @@index([supplierId])
  @@index([status])
}

model PurchaseItem {
  id         Int      @id @default(autoincrement())
  purchase   Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)
  purchaseId Int
  product    String   // Name of purchased product
  quantity   Int
  price      Float
  total      Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([purchaseId])
}

model Settings {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     String
  section   String?  // Settings section such as "general", "invoices", "system"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([key])
}

// نموذج المقادير (التقادير)
model RecipeItem {
  id              Int      @id @default(autoincrement())
  product         Product  @relation(fields: [productId], references: [id]) // المنتج المركب
  productId       Int
  ingredient      Product  @relation("IngredientProduct", fields: [ingredientId], references: [id]) // المكون
  ingredientId    Int
  quantity        Float    // كمية المكون
  unit            String?  // وحدة القياس
  notes           String?  // ملاحظات
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([productId, ingredientId])
  @@index([productId])
  @@index([ingredientId])
}

// نموذج خطة الإنتاج
model ProductionPlan {
  id                  Int                   @id @default(autoincrement())
  product             Product               @relation(fields: [productId], references: [id])
  productId           Int
  quantity            Int                   // كمية الإنتاج المخططة
  plannedDate         DateTime              // تاريخ الإنتاج المخطط
  actualDate          DateTime?             // تاريخ الإنتاج الفعلي
  productionDate      DateTime?             // تاريخ الإنتاج
  expiryDate          DateTime?             // تاريخ انتهاء الصلاحية
  batchNumber         String?               // رقم الدفعة
  status              String                // حالة خطة الإنتاج (PENDING, SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED)
  notes               String?               // ملاحظات
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  details             ProductionPlanDetail[]
  wastage             ProductionWastage[]
  qualityChecks       QualityCheck[]

  @@index([productId])
  @@index([status])
  @@index([plannedDate])
  @@index([batchNumber])
  @@index([expiryDate])
}

// نموذج تفاصيل خطة الإنتاج
model ProductionPlanDetail {
  id                  Int                   @id @default(autoincrement())
  productionPlan      ProductionPlan        @relation(fields: [productionPlanId], references: [id])
  productionPlanId    Int
  ingredient          Product               @relation(fields: [ingredientId], references: [id])
  ingredientId        Int
  requiredQuantity    Float                 // الكمية المطلوبة
  availableQuantity   Float                 // الكمية المتوفرة
  unit                String?               // وحدة القياس
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([productionPlanId])
  @@index([ingredientId])
}

// نموذج هدر الإنتاج
model ProductionWastage {
  id                  Int                   @id @default(autoincrement())
  productionPlan      ProductionPlan        @relation(fields: [productionPlanId], references: [id])
  productionPlanId    Int
  ingredient          Product               @relation(fields: [ingredientId], references: [id])
  ingredientId        Int
  quantity            Float                 // كمية الهدر
  unit                String?               // وحدة القياس
  reason              String?               // سبب الهدر
  cost                Float?                // تكلفة الهدر
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([productionPlanId])
  @@index([ingredientId])
}

// نموذج فحص الجودة
model QualityCheck {
  id                  Int                   @id @default(autoincrement())
  productionPlan      ProductionPlan        @relation(fields: [productionPlanId], references: [id])
  productionPlanId    Int
  checkDate           DateTime              // تاريخ الفحص
  checkedBy           String?               // تم الفحص بواسطة
  status              String                // حالة الفحص (PASSED, FAILED, PENDING)
  notes               String?               // ملاحظات
  parameters          QualityParameter[]    // معايير الجودة
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([productionPlanId])
  @@index([status])
  @@index([checkDate])
}

// نموذج معايير الجودة
model QualityParameter {
  id                  Int                   @id @default(autoincrement())
  qualityCheck        QualityCheck          @relation(fields: [qualityCheckId], references: [id])
  qualityCheckId      Int
  name                String                // اسم المعيار
  expectedValue       String?               // القيمة المتوقعة
  actualValue         String?               // القيمة الفعلية
  isPassed            Boolean               // هل اجتاز الفحص
  notes               String?               // ملاحظات
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([qualityCheckId])
}

// نموذج طلب الشراء
model PurchaseRequest {
  id                  Int                   @id @default(autoincrement())
  title               String                // عنوان الطلب
  status              String                // حالة الطلب (PENDING, APPROVED, REJECTED, COMPLETED)
  requestedBy         String                // تم الطلب بواسطة
  requestDate         DateTime              // تاريخ الطلب
  approvedBy          String?               // تمت الموافقة بواسطة
  approvalDate        DateTime?             // تاريخ الموافقة
  notes               String?               // ملاحظات
  items               PurchaseRequestItem[] // عناصر الطلب
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([status])
  @@index([requestDate])
}

// نموذج عنصر طلب الشراء
model PurchaseRequestItem {
  id                  Int                   @id @default(autoincrement())
  purchaseRequest     PurchaseRequest       @relation(fields: [purchaseRequestId], references: [id])
  purchaseRequestId   Int
  product             Product               @relation(fields: [productId], references: [id])
  productId           Int
  quantity            Float                 // الكمية المطلوبة
  unit                String?               // وحدة القياس
  estimatedPrice      Float?                // السعر التقديري
  notes               String?               // ملاحظات
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([purchaseRequestId])
  @@index([productId])
}

// نموذج الإشعارات
model Notification {
  id                  Int                   @id @default(autoincrement())
  title               String                // عنوان الإشعار
  message             String                // رسالة الإشعار
  type                String                // نوع الإشعار (PRODUCTION, QUALITY, INVENTORY, EXPIRY, SYSTEM)
  relatedId           String?               // معرف العنصر المرتبط بالإشعار
  relatedType         String?               // نوع العنصر المرتبط بالإشعار
  isRead              Boolean               @default(false) // هل تم قراءة الإشعار
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([type])
  @@index([isRead])
  @@index([createdAt])
}

// نموذج سجلات الأمان
model SecurityLog {
  id                  Int                   @id @default(autoincrement())
  type                String                // نوع الحدث الأمني
  userId              Int?                  // معرف المستخدم (إذا كان متاحاً)
  user                User?                 @relation(fields: [userId], references: [id])
  ip                  String?               // عنوان IP
  userAgent           String?               // معلومات المتصفح
  path                String?               // المسار المطلوب
  method              String?               // طريقة HTTP
  details             String?               // تفاصيل إضافية
  severity            String                // مستوى الخطورة (LOW, MEDIUM, HIGH, CRITICAL)
  timestamp           DateTime              @default(now()) // وقت الحدث
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt

  @@index([type])
  @@index([userId])
  @@index([ip])
  @@index([severity])
  @@index([timestamp])
  @@index([type, severity])
  @@index([userId, timestamp])
  @@index([ip, timestamp])
}

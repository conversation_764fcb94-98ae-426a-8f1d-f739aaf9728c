import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

/**
 * Middleware محسن للأمان والحماية
 */
export async function middleware(request: NextRequest) {
  const response = NextResponse.next();

  // إضافة headers الأمان
  addSecurityHeaders(response);

  // التحقق من CORS
  if (request.method === 'OPTIONS') {
    return handleCORS(request);
  }

  // حماية مسارات API
  if (request.nextUrl.pathname.startsWith('/api/')) {
    return await handleApiProtection(request);
  }

  // حماية الصفحات المحمية
  if (isProtectedRoute(request.nextUrl.pathname)) {
    return await handlePageProtection(request);
  }

  return response;
}

/**
 * إضافة headers الأمان
 */
function addSecurityHeaders(response: NextResponse) {
  // منع تضمين الصفحة في iframe
  response.headers.set('X-Frame-Options', 'DENY');
  
  // منع MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff');
  
  // تفعيل XSS protection
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Content Security Policy
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "font-src 'self' data:; " +
    "connect-src 'self'; " +
    "frame-ancestors 'none';"
  );
  
  // Referrer Policy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Permissions Policy
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), payment=()'
  );
}

/**
 * التعامل مع CORS
 */
function handleCORS(request: NextRequest) {
  const allowedOrigins = process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'];
  const origin = request.headers.get('origin');
  
  const response = new NextResponse(null, { status: 200 });
  
  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  }
  
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Max-Age', '86400');
  
  return response;
}

/**
 * حماية مسارات API
 */
async function handleApiProtection(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // المسارات العامة التي لا تحتاج حماية
  const publicApiRoutes = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/[...nextauth]',
    '/api/health'
  ];
  
  const isPublicRoute = publicApiRoutes.some(route => 
    pathname.startsWith(route.replace('[...nextauth]', ''))
  );
  
  if (isPublicRoute) {
    return NextResponse.next();
  }
  
  // التحقق من وجود token
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET 
  });
  
  if (!token) {
    return NextResponse.json(
      { error: 'غير مصرح لك بالوصول' },
      { status: 401 }
    );
  }
  
  return NextResponse.next();
}

/**
 * حماية الصفحات المحمية
 */
async function handlePageProtection(request: NextRequest) {
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET 
  });
  
  if (!token) {
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('callbackUrl', request.url);
    return NextResponse.redirect(loginUrl);
  }
  
  return NextResponse.next();
}

/**
 * التحقق من كون المسار محمي
 */
function isProtectedRoute(pathname: string): boolean {
  const protectedRoutes = [
    '/dashboard',
    '/admin',
    '/profile',
    '/settings',
    '/invoices',
    '/customers',
    '/products',
    '/inventory',
    '/reports'
  ];
  
  return protectedRoutes.some(route => pathname.startsWith(route));
}

/**
 * كشف المحاولات المشبوهة
 */
function detectSuspiciousActivity(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent') || '';
  const pathname = request.nextUrl.pathname;
  
  // كشف User Agents المشبوهة
  const suspiciousUserAgents = [
    'sqlmap',
    'nikto',
    'nmap',
    'masscan',
    'nessus',
    'openvas',
    'burp',
    'zap'
  ];
  
  if (suspiciousUserAgents.some(agent => 
    userAgent.toLowerCase().includes(agent)
  )) {
    return true;
  }
  
  // كشف محاولات SQL Injection في URL
  const sqlInjectionPatterns = [
    /union\s+select/i,
    /or\s+1\s*=\s*1/i,
    /drop\s+table/i,
    /insert\s+into/i,
    /delete\s+from/i,
    /update\s+set/i
  ];
  
  if (sqlInjectionPatterns.some(pattern => pattern.test(pathname))) {
    return true;
  }
  
  // كشف محاولات XSS
  const xssPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i
  ];
  
  if (xssPatterns.some(pattern => pattern.test(pathname))) {
    return true;
  }
  
  return false;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$|.*\\.ico$).*)',
  ],
};

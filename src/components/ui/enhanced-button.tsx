import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all-smooth focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 button-text",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 hover-lift shadow-sm",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 hover-lift shadow-sm",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground hover-lift",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 hover-lift shadow-sm",
        ghost: "hover:bg-accent hover:text-accent-foreground transition-colors-smooth",
        link: "text-primary underline-offset-4 hover:underline transition-colors-smooth",
        gradient: "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70 hover-lift shadow-md",
        success: "bg-success text-success-foreground hover:bg-success/90 hover-lift shadow-sm",
        warning: "bg-warning text-warning-foreground hover:bg-warning/90 hover-lift shadow-sm",
        info: "bg-info text-info-foreground hover:bg-info/90 hover-lift shadow-sm",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-lg px-8",
        xl: "h-12 rounded-xl px-10 text-base",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12",
      },
      loading: {
        true: "cursor-not-allowed",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      loading: false,
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    asChild = false, 
    loading = false,
    leftIcon,
    rightIcon,
    children,
    disabled,
    ...props 
  }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, loading, className }))}
        ref={ref}
        disabled={disabled || loading}
        aria-busy={loading ? "true" : undefined}
        {...props}
      >
        {loading && (
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        )}
        {!loading && leftIcon && (
          <span className="mr-2 flex items-center">
            {leftIcon}
          </span>
        )}
        {children}
        {!loading && rightIcon && (
          <span className="ml-2 flex items-center">
            {rightIcon}
          </span>
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

// مكون مجموعة أزرار
interface ButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: "horizontal" | "vertical"
  size?: VariantProps<typeof buttonVariants>["size"]
  variant?: VariantProps<typeof buttonVariants>["variant"]
}

const ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ className, orientation = "horizontal", children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="group"
        className={cn(
          "flex",
          orientation === "horizontal" ? "flex-row" : "flex-col",
          "[&>button]:rounded-none [&>button:first-child]:rounded-l-lg [&>button:last-child]:rounded-r-lg",
          orientation === "vertical" && "[&>button:first-child]:rounded-t-lg [&>button:first-child]:rounded-l-none [&>button:last-child]:rounded-b-lg [&>button:last-child]:rounded-r-none",
          "[&>button:not(:first-child)]:border-l-0",
          orientation === "vertical" && "[&>button:not(:first-child)]:border-l [&>button:not(:first-child)]:border-t-0",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ButtonGroup.displayName = "ButtonGroup"

// مكون زر عائم (Floating Action Button)
interface FABProps extends ButtonProps {
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
}

const FloatingActionButton = React.forwardRef<HTMLButtonElement, FABProps>(
  ({ className, position = "bottom-right", size = "icon-lg", variant = "default", ...props }, ref) => {
    const positionClasses = {
      "bottom-right": "fixed bottom-6 right-6",
      "bottom-left": "fixed bottom-6 left-6",
      "top-right": "fixed top-6 right-6",
      "top-left": "fixed top-6 left-6",
    }

    return (
      <Button
        ref={ref}
        className={cn(
          positionClasses[position],
          "rounded-full shadow-lg hover:shadow-xl z-50 animate-scale-in",
          className
        )}
        size={size}
        variant={variant}
        {...props}
      />
    )
  }
)
FloatingActionButton.displayName = "FloatingActionButton"

// مكون زر مع tooltip
interface TooltipButtonProps extends ButtonProps {
  tooltip?: string
  tooltipSide?: "top" | "bottom" | "left" | "right"
}

const TooltipButton = React.forwardRef<HTMLButtonElement, TooltipButtonProps>(
  ({ tooltip, tooltipSide = "top", ...props }, ref) => {
    if (!tooltip) {
      return <Button ref={ref} {...props} />
    }

    return (
      <div className="relative group">
        <Button ref={ref} {...props} />
        <div
          className={cn(
            "absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap",
            {
              "bottom-full left-1/2 transform -translate-x-1/2 mb-2": tooltipSide === "top",
              "top-full left-1/2 transform -translate-x-1/2 mt-2": tooltipSide === "bottom",
              "right-full top-1/2 transform -translate-y-1/2 mr-2": tooltipSide === "left",
              "left-full top-1/2 transform -translate-y-1/2 ml-2": tooltipSide === "right",
            }
          )}
        >
          {tooltip}
        </div>
      </div>
    )
  }
)
TooltipButton.displayName = "TooltipButton"

export { Button, ButtonGroup, FloatingActionButton, TooltipButton, buttonVariants }

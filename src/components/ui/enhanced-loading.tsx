import * as React from "react"
import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"

const loadingVariants = cva(
  "animate-spin",
  {
    variants: {
      variant: {
        spinner: "rounded-full border-2 border-current border-t-transparent",
        dots: "flex space-x-1",
        pulse: "rounded-full bg-current animate-pulse-soft",
        bars: "flex space-x-1",
        wave: "flex space-x-1",
      },
      size: {
        sm: "h-4 w-4",
        default: "h-6 w-6",
        lg: "h-8 w-8",
        xl: "h-12 w-12",
      },
      color: {
        primary: "text-primary",
        secondary: "text-secondary-foreground",
        muted: "text-muted-foreground",
        white: "text-white",
        current: "text-current",
      },
    },
    defaultVariants: {
      variant: "spinner",
      size: "default",
      color: "primary",
    },
  }
)

export interface LoadingProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  text?: string
  overlay?: boolean
}

const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ className, variant, size, color, text, overlay = false, ...props }, ref) => {
    const renderSpinner = () => {
      switch (variant) {
        case "dots":
          return (
            <div className={cn("flex space-x-1", className)}>
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className={cn(
                    "rounded-full bg-current animate-bounce",
                    size === "sm" && "h-1 w-1",
                    size === "default" && "h-1.5 w-1.5",
                    size === "lg" && "h-2 w-2",
                    size === "xl" && "h-3 w-3",
                    `text-${color}`
                  )}
                  style={{
                    animationDelay: `${i * 0.1}s`,
                  }}
                />
              ))}
            </div>
          )
        
        case "bars":
          return (
            <div className={cn("flex space-x-1", className)}>
              {[0, 1, 2, 3].map((i) => (
                <div
                  key={i}
                  className={cn(
                    "bg-current animate-pulse",
                    size === "sm" && "h-3 w-0.5",
                    size === "default" && "h-4 w-1",
                    size === "lg" && "h-6 w-1",
                    size === "xl" && "h-8 w-1.5",
                    `text-${color}`
                  )}
                  style={{
                    animationDelay: `${i * 0.15}s`,
                  }}
                />
              ))}
            </div>
          )
        
        case "wave":
          return (
            <div className={cn("flex space-x-1 items-end", className)}>
              {[0, 1, 2, 3, 4].map((i) => (
                <div
                  key={i}
                  className={cn(
                    "bg-current rounded-full",
                    size === "sm" && "h-2 w-1",
                    size === "default" && "h-3 w-1",
                    size === "lg" && "h-4 w-1.5",
                    size === "xl" && "h-6 w-2",
                    `text-${color}`
                  )}
                  style={{
                    animation: `wave 1.4s ease-in-out ${i * 0.1}s infinite`,
                  }}
                />
              ))}
            </div>
          )
        
        case "pulse":
          return (
            <div
              className={cn(
                loadingVariants({ variant: "pulse", size, color }),
                className
              )}
            />
          )
        
        default:
          return (
            <div
              className={cn(
                loadingVariants({ variant, size, color }),
                className
              )}
            />
          )
      }
    }

    const content = (
      <div
        ref={ref}
        className={cn(
          "flex flex-col items-center justify-center space-y-2",
          overlay && "fixed inset-0 bg-background/80 backdrop-blur-sm z-50"
        )}
        {...props}
      >
        {renderSpinner()}
        {text && (
          <p className={cn(
            "text-sm text-muted-foreground animate-pulse-soft",
            size === "sm" && "text-xs",
            size === "lg" && "text-base",
            size === "xl" && "text-lg"
          )}>
            {text}
          </p>
        )}
      </div>
    )

    return content
  }
)
Loading.displayName = "Loading"

// مكون Skeleton محسن
interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "text" | "circular" | "rectangular" | "rounded"
  animation?: "pulse" | "wave" | "none"
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant = "rectangular", animation = "pulse", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "bg-muted",
          {
            "rounded-full": variant === "circular",
            "rounded-md": variant === "rounded",
            "rounded-sm": variant === "rectangular",
            "rounded": variant === "text",
          },
          {
            "animate-pulse": animation === "pulse",
            "loading-shimmer": animation === "wave",
          },
          className
        )}
        {...props}
      />
    )
  }
)
Skeleton.displayName = "Skeleton"

// مكون LoadingButton
interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean
  loadingText?: string
  variant?: "default" | "outline" | "ghost"
  size?: "sm" | "default" | "lg"
}

const LoadingButton = React.forwardRef<HTMLButtonElement, LoadingButtonProps>(
  ({ 
    children, 
    loading = false, 
    loadingText, 
    disabled, 
    className,
    variant = "default",
    size = "default",
    ...props 
  }, ref) => {
    const baseClasses = "inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
    
    const variantClasses = {
      default: "bg-primary text-primary-foreground hover:bg-primary/90",
      outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
      ghost: "hover:bg-accent hover:text-accent-foreground",
    }
    
    const sizeClasses = {
      sm: "h-9 px-3 text-sm",
      default: "h-10 px-4 py-2",
      lg: "h-11 px-8",
    }

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <Loading
            variant="spinner"
            size="sm"
            color="current"
            className="mr-2"
          />
        )}
        {loading ? loadingText || children : children}
      </button>
    )
  }
)
LoadingButton.displayName = "LoadingButton"

// إضافة keyframes للـ wave animation
const waveKeyframes = `
  @keyframes wave {
    0%, 40%, 100% {
      transform: scaleY(0.4);
    }
    20% {
      transform: scaleY(1);
    }
  }
`

// إدراج الـ keyframes في الـ head
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = waveKeyframes
  document.head.appendChild(style)
}

// مكون Spinner محسن (دمج مع Spinner القديم)
interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'primary' | 'secondary' | 'destructive';
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size = 'md', variant = 'primary', ...props }, ref) => {
    const sizeMap = {
      sm: 'sm',
      md: 'default',
      lg: 'lg'
    } as const;

    const colorMap = {
      default: 'muted',
      primary: 'primary',
      secondary: 'secondary',
      destructive: 'current'
    } as const;

    return (
      <Loading
        ref={ref}
        variant="spinner"
        size={sizeMap[size]}
        color={colorMap[variant]}
        className={className}
        {...props}
      />
    );
  }
);
Spinner.displayName = 'Spinner';

export { Loading, Skeleton, LoadingButton, Spinner, loadingVariants }

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/enhanced-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/enhanced-card';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Download, Upload, FileText, Database, CheckCircle, AlertCircle, FileUp, FileDown } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';

interface ExportImportProps {
  onExport?: (format: string, options: any) => Promise<void>;
  onImport?: (file: File, options: any) => Promise<void>;
}

export function ExportImport({ onExport, onImport }: ExportImportProps) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('export');
  const [exportFormat, setExportFormat] = useState('excel');
  const [exportOptions, setExportOptions] = useState({
    includeInvoices: true,
    includeCustomers: true,
    includeProducts: true,
    includePayments: true,
    dateRange: 'all',
    startDate: '',
    endDate: '',
  });
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importOptions, setImportOptions] = useState({
    overwrite: false,
    validateData: true,
    skipErrors: false,
  });
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    details?: string;
  } | null>(null);

  const handleExport = async () => {
    try {
      setLoading(true);
      setProgress(0);
      setResult(null);
      
      // محاكاة التقدم
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 10;
        });
      }, 300);
      
      // محاكاة عملية التصدير
      if (onExport) {
        await onExport(exportFormat, exportOptions);
      } else {
        // محاكاة التصدير إذا لم يتم توفير دالة onExport
        await new Promise((resolve) => setTimeout(resolve, 3000));
      }
      
      clearInterval(interval);
      setProgress(100);
      
      // إنشاء رابط تنزيل وهمي
      const fileName = `amin_plus_export_${new Date().toISOString().split('T')[0]}.${exportFormat === 'excel' ? 'xlsx' : exportFormat}`;
      
      setResult({
        success: true,
        message: t('common.success'),
        details: `${t('settings.exportData')} ${fileName} ${t('common.success')}`,
      });
      
      toast.success(`${t('settings.exportData')} ${t('common.success')}`);
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      
      setResult({
        success: false,
        message: t('common.error'),
        details: `${t('settings.exportData')} ${t('common.error')}: ${error instanceof Error ? error.message : String(error)}`,
      });
      
      toast.error(`${t('settings.exportData')} ${t('common.error')}`);
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async () => {
    if (!importFile) {
      toast.error(t('common.error'));
      return;
    }
    
    try {
      setLoading(true);
      setProgress(0);
      setResult(null);
      
      // محاكاة التقدم
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 5;
        });
      }, 200);
      
      // محاكاة عملية الاستيراد
      if (onImport) {
        await onImport(importFile, importOptions);
      } else {
        // محاكاة الاستيراد إذا لم يتم توفير دالة onImport
        await new Promise((resolve) => setTimeout(resolve, 4000));
      }
      
      clearInterval(interval);
      setProgress(100);
      
      setResult({
        success: true,
        message: t('common.success'),
        details: `${t('settings.importData')} ${importFile.name} ${t('common.success')}`,
      });
      
      toast.success(`${t('settings.importData')} ${t('common.success')}`);
    } catch (error) {
      console.error('خطأ في استيراد البيانات:', error);
      
      setResult({
        success: false,
        message: t('common.error'),
        details: `${t('settings.importData')} ${t('common.error')}: ${error instanceof Error ? error.message : String(error)}`,
      });
      
      toast.error(`${t('settings.importData')} ${t('common.error')}`);
    } finally {
      setLoading(false);
      setImportFile(null);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setImportFile(files[0]);
      setResult(null);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{activeTab === 'export' ? t('settings.export') : t('settings.import')}</CardTitle>
        <CardDescription>
          {activeTab === 'export'
            ? t('settings.exportData')
            : t('settings.importData')}
        </CardDescription>
      </CardHeader>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mx-4">
          <TabsTrigger value="export" className="flex items-center gap-1">
            <FileDown className="h-4 w-4" />
            {t('settings.export')}
          </TabsTrigger>
          <TabsTrigger value="import" className="flex items-center gap-1">
            <FileUp className="h-4 w-4" />
            {t('settings.import')}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="export">
          <CardContent className="space-y-4">
            {result ? (
              <div className={`p-4 rounded-md ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
                <div className="flex items-start gap-3">
                  {result.success ? (
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                  )}
                  <div>
                    <h3 className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                      {result.message}
                    </h3>
                    <p className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                      {result.details}
                    </p>
                    {result.success && (
                      <Button variant="outline" size="sm" className="mt-2">
                        <Download className="h-4 w-4 ml-1" />
                        {t('common.download')}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ) : loading ? (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t('common.loading')}...</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} />
              </div>
            ) : (
              <>
                <div className="space-y-2">
                  <Label htmlFor="export_format">{t('common.format')}</Label>
                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant={exportFormat === 'excel' ? 'default' : 'outline'}
                      className="flex flex-col items-center justify-center h-20 gap-1"
                      onClick={() => setExportFormat('excel')}
                    >
                      <FileText className="h-8 w-8" />
                      <span>Excel</span>
                    </Button>
                    <Button
                      variant={exportFormat === 'csv' ? 'default' : 'outline'}
                      className="flex flex-col items-center justify-center h-20 gap-1"
                      onClick={() => setExportFormat('csv')}
                    >
                      <FileText className="h-8 w-8" />
                      <span>CSV</span>
                    </Button>
                    <Button
                      variant={exportFormat === 'json' ? 'default' : 'outline'}
                      className="flex flex-col items-center justify-center h-20 gap-1"
                      onClick={() => setExportFormat('json')}
                    >
                      <Database className="h-8 w-8" />
                      <span>JSON</span>
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>{t('common.data')}</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        id="include_invoices"
                        checked={exportOptions.includeInvoices}
                        onChange={(e) => setExportOptions({ ...exportOptions, includeInvoices: e.target.checked })}
                        className="h-4 w-4"
                      />
                      <Label htmlFor="include_invoices" className="cursor-pointer">
                        {t('invoices.title')}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        id="include_customers"
                        checked={exportOptions.includeCustomers}
                        onChange={(e) => setExportOptions({ ...exportOptions, includeCustomers: e.target.checked })}
                        className="h-4 w-4"
                      />
                      <Label htmlFor="include_customers" className="cursor-pointer">
                        {t('customers.title')}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        id="include_products"
                        checked={exportOptions.includeProducts}
                        onChange={(e) => setExportOptions({ ...exportOptions, includeProducts: e.target.checked })}
                        className="h-4 w-4"
                      />
                      <Label htmlFor="include_products" className="cursor-pointer">
                        {t('products.title')}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <input
                        type="checkbox"
                        id="include_payments"
                        checked={exportOptions.includePayments}
                        onChange={(e) => setExportOptions({ ...exportOptions, includePayments: e.target.checked })}
                        className="h-4 w-4"
                      />
                      <Label htmlFor="include_payments" className="cursor-pointer">
                        {t('payments.title')}
                      </Label>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="date_range">{t('reports.dateRange')}</Label>
                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant={exportOptions.dateRange === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setExportOptions({ ...exportOptions, dateRange: 'all' })}
                    >
                      {t('common.all')}
                    </Button>
                    <Button
                      variant={exportOptions.dateRange === 'this_month' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setExportOptions({ ...exportOptions, dateRange: 'this_month' })}
                    >
                      {t('reports.thisMonth')}
                    </Button>
                    <Button
                      variant={exportOptions.dateRange === 'custom' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setExportOptions({ ...exportOptions, dateRange: 'custom' })}
                    >
                      {t('reports.custom')}
                    </Button>
                  </div>
                </div>
                
                {exportOptions.dateRange === 'custom' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start_date">{t('reports.startDate')}</Label>
                      <Input
                        id="start_date"
                        type="date"
                        value={exportOptions.startDate}
                        onChange={(e) => setExportOptions({ ...exportOptions, startDate: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end_date">{t('reports.endDate')}</Label>
                      <Input
                        id="end_date"
                        type="date"
                        value={exportOptions.endDate}
                        onChange={(e) => setExportOptions({ ...exportOptions, endDate: e.target.value })}
                      />
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
          
          {!result && (
            <CardFooter className="border-t pt-4">
              <Button onClick={handleExport} disabled={loading}>
                {loading ? (
                  <>
                    <span className="animate-spin mr-1">⏳</span>
                    {t('common.loading')}...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 ml-1" />
                    {t('settings.export')}
                  </>
                )}
              </Button>
            </CardFooter>
          )}
        </TabsContent>
        
        <TabsContent value="import">
          <CardContent className="space-y-4">
            {result ? (
              <div className={`p-4 rounded-md ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
                <div className="flex items-start gap-3">
                  {result.success ? (
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                  )}
                  <div>
                    <h3 className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                      {result.message}
                    </h3>
                    <p className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                      {result.details}
                    </p>
                  </div>
                </div>
              </div>
            ) : loading ? (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t('common.loading')}...</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} />
              </div>
            ) : (
              <>
                <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center">
                  <Upload className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500 mb-2">
                    {importFile
                      ? importFile.name
                      : t('settings.importData')}
                  </p>
                  <Input
                    id="import_file"
                    type="file"
                    accept=".xlsx,.csv,.json"
                    onChange={handleFileChange}
                    className="max-w-xs"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    {t('common.supportedFormats')}: Excel, CSV, JSON
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label>{t('common.options')}</Label>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="overwrite" className="cursor-pointer">
                        {t('common.overwrite')}
                        <p className="text-xs text-gray-500">
                          {t('common.overwriteDescription')}
                        </p>
                      </Label>
                      <input
                        type="checkbox"
                        id="overwrite"
                        checked={importOptions.overwrite}
                        onChange={(e) => setImportOptions({ ...importOptions, overwrite: e.target.checked })}
                        className="h-4 w-4"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="validate_data" className="cursor-pointer">
                        {t('common.validateData')}
                        <p className="text-xs text-gray-500">
                          {t('common.validateDataDescription')}
                        </p>
                      </Label>
                      <input
                        type="checkbox"
                        id="validate_data"
                        checked={importOptions.validateData}
                        onChange={(e) => setImportOptions({ ...importOptions, validateData: e.target.checked })}
                        className="h-4 w-4"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="skip_errors" className="cursor-pointer">
                        {t('common.skipErrors')}
                        <p className="text-xs text-gray-500">
                          {t('common.skipErrorsDescription')}
                        </p>
                      </Label>
                      <input
                        type="checkbox"
                        id="skip_errors"
                        checked={importOptions.skipErrors}
                        onChange={(e) => setImportOptions({ ...importOptions, skipErrors: e.target.checked })}
                        className="h-4 w-4"
                      />
                    </div>
                  </div>
                </div>
              </>
            )}
          </CardContent>
          
          {!result && (
            <CardFooter className="border-t pt-4">
              <Button
                onClick={handleImport}
                disabled={loading || !importFile}
              >
                {loading ? (
                  <>
                    <span className="animate-spin mr-1">⏳</span>
                    {t('common.loading')}...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 ml-1" />
                    {t('settings.import')}
                  </>
                )}
              </Button>
            </CardFooter>
          )}
        </TabsContent>
      </Tabs>
    </Card>
  );
}

// مكون Progress المستخدم في الكود
function Progress({ value }: { value: number }) {
  return (
    <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
      <div
        className="h-full bg-primary transition-all duration-300 ease-in-out"
        style={{ width: `${value}%` }}
      />
    </div>
  );
}

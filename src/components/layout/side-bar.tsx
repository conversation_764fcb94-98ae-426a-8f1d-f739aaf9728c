'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
    LayoutDashboard,
    Users,
    FileText,
    Settings,
    CreditCard,
    Package,
    LogOut,
    Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import AppLogo from '@/components/ui/app-logo';
import { useI18n } from '@/lib/i18n';

interface SidebarProps {
    readonly isOpen: boolean;
}

export default function Sidebar({ isOpen }: SidebarProps) {
    const pathname = usePathname();
    const { t, language } = useI18n();

    // قائمة الروابط - Navigation links
    const navItems = [
        {
            title: 'لوحة التحكم',
            href: '/dashboard',
            icon: LayoutDashboard,
        },
        {
            title: 'العملاء',
            href: '/dashboard/customers',
            icon: Users,
        },
        {
            title: 'المنتجات',
            href: '/dashboard/products',
            icon: Package,
        },
        {
            title: 'الفواتير',
            href: '/dashboard/invoices',
            icon: FileText,
        },
        {
            title: 'التقارير',
            href: '/dashboard/reports',
            icon: Monitor,
        },
        {
            title: 'الإعدادات',
            href: '/dashboard/settings',
            icon: Settings,
        },
    ];

    const isActive = (path: string) => {
        // للتحقق من أن المسار الحالي هو المسار النشط
        // Check if current path is the active path
        if (path === '/dashboard' && pathname === '/dashboard') {
            return true;
        }

        // للتحقق من أن المسار الحالي هو أحد المسارات الفرعية للمسار الرئيسي
        // Check if current path is a subpath of the main path
        return path !== '/dashboard' && pathname.startsWith(path);
    };

    return (
        <aside
            className={cn(
                `fixed inset-y-0 ${language === 'ar' ? 'right-0' : 'left-0'} z-50 flex h-full w-64 flex-col ${language === 'ar' ? 'border-l' : 'border-r'} bg-white transition-all duration-300 ease-in-out dark:bg-gray-900`,
                isOpen ? "translate-x-0" : language === 'ar' ? "translate-x-full" : "-translate-x-full"
            )}
        >
            {/* رأس الشريط الجانبي - Sidebar header */}
            <div className="flex h-16 items-center justify-between border-b px-4">
                <AppLogo />
            </div>

            {/* قائمة التنقل - Navigation menu */}
            <nav className="flex-1 overflow-y-auto p-4">
                <ul className="space-y-2">
                    {navItems.map((item) => (
                        <li key={item.href}>
                            <Link
                                href={item.href}
                                className={cn(
                                    "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                                    isActive(item.href)
                                        ? "bg-primary/10 text-primary"
                                        : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
                                )}
                            >
                                <item.icon className={`${language === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5`} />
                                <span>{item.title}</span>
                            </Link>
                        </li>
                    ))}
                </ul>
            </nav>

            {/* تذييل الشريط الجانبي - Sidebar footer */}
            <div className="border-t p-4">
                <div className="mb-4 flex items-center">
                    <div className={`${language === 'ar' ? 'ml-3' : 'mr-3'}`}>
                        <p className="text-sm font-medium">{t('sidebar.testUser')}</p>
                        <p className="text-xs text-gray-500"><EMAIL></p>
                    </div>
                </div>
                <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => {
                        // تسجيل الخروج
                        window.location.href = '/api/auth/signout';
                    }}
                >
                    <LogOut className={`${language === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
                    <span>تسجيل الخروج</span>
                </Button>
            </div>
        </aside>
    );
}

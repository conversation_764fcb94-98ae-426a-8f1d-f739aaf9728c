import React from 'react';
import { render, screen } from '@testing-library/react';
import { PermissionGuard } from '../auth/permission-guard';
import { usePermission } from '@/hooks/usePermission';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import '@testing-library/jest-dom';

// إسكات تحذيرات الاختبار
// Silence test warnings
jest.mock('@/hooks/usePermission', () => ({
  usePermission: jest.fn(),
}));

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

describe('PermissionGuard Component', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    // تهيئة قيم افتراضية للـ hooks
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });

    // إعادة تعيين عداد الاستدعاءات قبل كل اختبار
    mockPush.mockClear();
  });

  it('renders children when user has permission', () => {
    // تهيئة قيمة لـ useSession
    (useSession as jest.Mock).mockReturnValue({
      status: 'authenticated',
    });

    // تهيئة قيمة لـ usePermission
    (usePermission as jest.Mock).mockReturnValue({ hasPermission: () => true, hasAnyPermission: () => true });

    // اختبار المكون
    render(
      <PermissionGuard permission="MANAGE_USERS">
        <div data-testid="protected-content">محتوى محمي</div>
      </PermissionGuard>
    );

    // التحقق من استدعاء useSession
    expect(useSession).toHaveBeenCalled();

    // التحقق من استدعاء usePermission
    expect(usePermission).toHaveBeenCalled();

    // التحقق من ظهور المحتوى المحمي
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });

  it('redirects to login when user is not authenticated', () => {
    // تهيئة قيمة لـ useSession
    (useSession as jest.Mock).mockReturnValue({
      status: 'unauthenticated',
    });

    // اختبار المكون
    render(
      <PermissionGuard permission="MANAGE_USERS">
        <div data-testid="protected-content">محتوى محمي</div>
      </PermissionGuard>
    );

    // التحقق من استدعاء دالة التوجيه إلى صفحة تسجيل الدخول
    expect(mockPush).toHaveBeenCalledWith('/auth/login');

    // التحقق من عدم ظهور المحتوى المحمي
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });

  it('shows loading state when session is loading', () => {
    // تهيئة قيمة لـ useSession
    (useSession as jest.Mock).mockReturnValue({
      status: 'loading',
    });

    // اختبار المكون
    render(
      <PermissionGuard permission="MANAGE_USERS">
        <div data-testid="protected-content">محتوى محمي</div>
      </PermissionGuard>
    );

    // التحقق من ظهور حالة التحميل
    expect(screen.getByText('جاري التحميل...')).toBeInTheDocument();

    // التحقق من عدم ظهور المحتوى المحمي
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });

  it('shows fallback content when user does not have permission', () => {
    // تهيئة قيمة لـ useSession
    (useSession as jest.Mock).mockReturnValue({
      status: 'authenticated',
    });

    // تهيئة قيمة لـ usePermission
    (usePermission as jest.Mock).mockReturnValue({ hasPermission: () => false, hasAnyPermission: () => false });

    // اختبار المكون مع محتوى بديل
    render(
      <PermissionGuard
        permission="MANAGE_USERS"
        fallback={<div data-testid="fallback-content">ليس لديك صلاحية</div>}
      >
        <div data-testid="protected-content">محتوى محمي</div>
      </PermissionGuard>
    );

    // التحقق من ظهور المحتوى البديل
    expect(screen.getByTestId('fallback-content')).toBeInTheDocument();

    // التحقق من عدم ظهور المحتوى المحمي
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });

  it('shows default error message when no fallback is provided and user does not have permission', () => {
    // تهيئة قيمة لـ useSession
    (useSession as jest.Mock).mockReturnValue({
      status: 'authenticated',
    });

    // تهيئة قيمة لـ usePermission
    (usePermission as jest.Mock).mockReturnValue({ hasPermission: () => false, hasAnyPermission: () => false });

    // اختبار المكون بدون محتوى بديل
    render(
      <PermissionGuard permission="MANAGE_USERS">
        <div data-testid="protected-content">محتوى محمي</div>
      </PermissionGuard>
    );

    // التحقق من ظهور رسالة الخطأ الافتراضية
    expect(screen.getByText('غير مصرح لك بالوصول إلى هذه الصفحة')).toBeInTheDocument();

    // التحقق من عدم ظهور المحتوى المحمي
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });
});

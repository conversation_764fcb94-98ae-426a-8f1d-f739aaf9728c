import { NextRequest } from 'next/server';

interface RateLimitConfig {
  requests: number;
  windowMs: number;
}

interface RateLimitResult {
  success: boolean;
  remaining: number;
  resetTime: number;
}

// تخزين مؤقت للطلبات في الذاكرة (للتطوير)
// In-memory storage for requests (for development)
const requestCounts = new Map<string, { count: number; resetTime: number }>();

/**
 * تطبيق Rate Limiting للحماية من إساءة الاستخدام
 * Apply rate limiting to protect against abuse
 */
export async function rateLimit(
  req: NextRequest,
  config: RateLimitConfig
): Promise<RateLimitResult> {
  const ip = getClientIP(req);
  const key = `${ip}:${req.nextUrl.pathname}`;
  const now = Date.now();
  const windowStart = now - config.windowMs;

  // تنظيف البيانات القديمة
  cleanupOldEntries(windowStart);

  const current = requestCounts.get(key);

  if (!current || current.resetTime <= now) {
    // إنشاء نافذة جديدة
    requestCounts.set(key, {
      count: 1,
      resetTime: now + config.windowMs
    });

    return {
      success: true,
      remaining: config.requests - 1,
      resetTime: now + config.windowMs
    };
  }

  if (current.count >= config.requests) {
    return {
      success: false,
      remaining: 0,
      resetTime: current.resetTime
    };
  }

  // زيادة العداد
  current.count++;
  requestCounts.set(key, current);

  return {
    success: true,
    remaining: config.requests - current.count,
    resetTime: current.resetTime
  };
}

/**
 * تنظيف البيانات القديمة من الذاكرة
 */
function cleanupOldEntries(windowStart: number) {
  for (const [key, data] of requestCounts.entries()) {
    if (data.resetTime <= windowStart) {
      requestCounts.delete(key);
    }
  }
}

/**
 * الحصول على عنوان IP الخاص بالعميل
 */
function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const real = req.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (real) {
    return real.trim();
  }
  
  return 'unknown';
}

/**
 * إعدادات Rate Limiting المختلفة
 */
export const RATE_LIMITS = {
  // API عام - 100 طلب في الدقيقة
  API_GENERAL: { requests: 100, windowMs: 60 * 1000 },

  // تسجيل الدخول - 5 محاولات في 15 دقيقة
  AUTH_LOGIN: { requests: 5, windowMs: 15 * 60 * 1000 },

  // إنشاء الحساب - 3 محاولات في الساعة
  AUTH_REGISTER: { requests: 3, windowMs: 60 * 60 * 1000 },

  // إعادة تعيين كلمة المرور - 3 محاولات في الساعة
  AUTH_RESET: { requests: 3, windowMs: 60 * 60 * 1000 },

  // العمليات الحساسة - 10 طلبات في الدقيقة
  SENSITIVE_OPS: { requests: 10, windowMs: 60 * 1000 },

  // رفع الملفات - 20 ملف في الساعة
  FILE_UPLOAD: { requests: 20, windowMs: 60 * 60 * 1000 }
};

/**
 * تنظيف store للاختبارات
 * Clear store for testing
 */
export function clearRateLimitStore() {
  requestCounts.clear();
}

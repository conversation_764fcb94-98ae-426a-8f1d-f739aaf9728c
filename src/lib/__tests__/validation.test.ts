import { describe, it, expect } from '@jest/globals';
import { 
  sanitizeString, 
  validateEmail, 
  validatePassword, 
  validateInput 
} from '../validation';

describe('Validation Utils', () => {
  describe('sanitizeString', () => {
    it('should remove script tags', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello World';
      const result = sanitizeString(maliciousInput);
      expect(result).toBe('Hello World');
    });

    it('should remove javascript protocols', () => {
      const maliciousInput = 'javascript:alert("xss")';
      const result = sanitizeString(maliciousInput);
      expect(result).toBe('alert("xss")');
    });

    it('should remove event handlers', () => {
      const maliciousInput = 'onclick="alert(1)" Hello';
      const result = sanitizeString(maliciousInput);
      expect(result).toBe(' Hello');
    });

    it('should remove HTML tags', () => {
      const input = '<div>Hello <span>World</span></div>';
      const result = sanitizeString(input);
      expect(result).toBe('Hello World');
    });

    it('should trim whitespace', () => {
      const input = '  Hello World  ';
      const result = sanitizeString(input);
      expect(result).toBe('Hello World');
    });

    it('should handle non-string input', () => {
      const result = sanitizeString(123 as any);
      expect(result).toBe('');
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        '<EMAIL>',
        'user@.com',
        'user@domain.',
        ''
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('validatePassword', () => {
    it('should validate strong passwords', () => {
      const strongPassword = 'StrongP@ssw0rd123';
      const result = validatePassword(strongPassword);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject passwords that are too short', () => {
      const shortPassword = 'Short1!';
      const result = validatePassword(shortPassword);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    });

    it('should require uppercase letters', () => {
      const password = 'lowercase123!';
      const result = validatePassword(password);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
    });

    it('should require lowercase letters', () => {
      const password = 'UPPERCASE123!';
      const result = validatePassword(password);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
    });

    it('should require numbers', () => {
      const password = 'NoNumbers!';
      const result = validatePassword(password);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
    });

    it('should require special characters', () => {
      const password = 'NoSpecialChars123';
      const result = validatePassword(password);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
    });
  });

  describe('validateInput', () => {
    it('should validate login data correctly', () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const result = validateInput(loginData, '/api/auth/login');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitizedData).toBeDefined();
    });

    it('should reject invalid login data', () => {
      const invalidLoginData = {
        email: 'invalid-email',
        password: ''
      };

      const result = validateInput(invalidLoginData, '/api/auth/login');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should validate customer data correctly', () => {
      const customerData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Main St'
      };

      const result = validateInput(customerData, '/api/customers');
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toBeDefined();
    });

    it('should validate product data correctly', () => {
      const productData = {
        name: 'Test Product',
        barcode: '1234567890',
        price: 99.99,
        cost: 50.00,
        stockQty: 100,
        categoryId: 'cat123'
      };

      const result = validateInput(productData, '/api/products');
      
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid product data', () => {
      const invalidProductData = {
        name: '',
        price: -10, // Negative price
        stockQty: -5 // Negative stock
      };

      const result = validateInput(invalidProductData, '/api/products');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should sanitize malicious input', () => {
      const maliciousData = {
        name: '<script>alert("xss")</script>Clean Name',
        description: 'onclick="malicious()" Good description'
      };

      const result = validateInput(maliciousData, '/api/customers');
      
      expect(result.sanitizedData?.name).toBe('Clean Name');
      expect(result.sanitizedData?.description).toBe(' Good description');
    });

    it('should handle unknown paths with basic validation', () => {
      const data = {
        field1: 'value1',
        field2: 'value2'
      };

      const result = validateInput(data, '/api/unknown');
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toBeDefined();
    });

    it('should reject data that is too large', () => {
      const largeData = {
        field: 'x'.repeat(2 * 1024 * 1024) // 2MB string
      };

      const result = validateInput(largeData, '/api/unknown');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('حجم البيانات كبير جداً');
    });

    it('should reject deeply nested objects', () => {
      let deepObject: any = {};
      let current = deepObject;
      
      // Create object with depth > 10
      for (let i = 0; i < 15; i++) {
        current.nested = {};
        current = current.nested;
      }

      const result = validateInput(deepObject, '/api/unknown');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('البيانات معقدة جداً');
    });

    it('should handle validation errors gracefully', () => {
      // Test with invalid data that might cause validation to throw
      const invalidData = null;

      const result = validateInput(invalidData, '/api/test');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('خطأ في التحقق من البيانات');
    });
  });
});

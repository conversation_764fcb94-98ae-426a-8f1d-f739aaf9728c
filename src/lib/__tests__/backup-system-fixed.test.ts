import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { createDatabaseBackup, listBackups, restoreFromBackup } from '../backup-system';

// Mock fs/promises
jest.mock('fs/promises', () => ({
  access: jest.fn().mockResolvedValue(undefined),
  mkdir: jest.fn().mockResolvedValue(undefined),
  copyFile: jest.fn().mockResolvedValue(undefined),
  stat: jest.fn().mockResolvedValue({ 
    size: 1024 * 1024, 
    birthtime: new Date(),
    isFile: () => true,
    isDirectory: () => false
  }),
  readdir: jest.fn().mockResolvedValue([]),
  unlink: jest.fn().mockResolvedValue(undefined),
  readFile: jest.fn().mockResolvedValue(Buffer.from('test data')),
  writeFile: jest.fn().mockResolvedValue(undefined)
}));

jest.mock('../security-logger', () => ({
  logSecurityEvent: jest.fn()
}));

describe('Backup System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createDatabaseBackup', () => {
    it('should create a basic backup successfully', async () => {
      const result = await createDatabaseBackup({ enabled: true });

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.filePath).toContain('backup-'); // تم تحديث من backupPath إلى filePath
    });

    it('should create a compressed backup', async () => {
      const result = await createDatabaseBackup({
        enabled: true,
        compressionEnabled: true
      });

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.filePath).toContain('.gz');
    });

    it('should create an encrypted backup', async () => {
      const result = await createDatabaseBackup({
        enabled: true,
        encryptionEnabled: true
      });

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.filePath).toContain('.enc');
    });
  });

  describe('listBackups', () => {
    it('should list available backups', async () => {
      const result = await listBackups();
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('restoreFromBackup', () => {
    it('should restore from backup successfully', async () => {
      const result = await restoreFromBackup('test-backup.db');
      
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });
  });
});

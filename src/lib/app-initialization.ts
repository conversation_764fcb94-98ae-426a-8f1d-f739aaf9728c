import { initializeScheduledTasks } from './scheduled-tasks';
import { logSecurityEvent } from './security-logger';

/**
 * تهيئة التطبيق عند البدء
 */
export async function initializeApplication(): Promise<void> {
  try {
    console.log('🚀 Initializing Amin Plus application...');

    // تهيئة المهام المجدولة
    initializeScheduledTasks();
    console.log('✅ Scheduled tasks initialized');

    // تسجيل بدء التطبيق
    await logSecurityEvent({
      type: 'SYSTEM',
      details: 'Application started successfully',
      severity: 'LOW'
    });

    console.log('✅ Application initialization completed');

  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    
    // تسجيل فشل التهيئة
    await logSecurityEvent({
      type: 'SYSTEM',
      details: `Application initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      severity: 'CRITICAL'
    });

    throw error;
  }
}

/**
 * إيقاف التطبيق بشكل آمن
 */
export async function shutdownApplication(): Promise<void> {
  try {
    console.log('🛑 Shutting down Amin Plus application...');

    // إيقاف المهام المجدولة
    const { taskScheduler } = await import('./scheduled-tasks');
    taskScheduler.stopAll();
    console.log('✅ Scheduled tasks stopped');

    // تسجيل إيقاف التطبيق
    await logSecurityEvent({
      type: 'SYSTEM',
      details: 'Application shutdown initiated',
      severity: 'LOW'
    });

    console.log('✅ Application shutdown completed');

  } catch (error) {
    console.error('❌ Application shutdown failed:', error);
  }
}

// معالجة إشارات النظام للإيقاف الآمن
if (typeof process !== 'undefined') {
  process.on('SIGTERM', async () => {
    console.log('Received SIGTERM signal');
    await shutdownApplication();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    console.log('Received SIGINT signal');
    await shutdownApplication();
    process.exit(0);
  });

  process.on('uncaughtException', async (error) => {
    console.error('Uncaught Exception:', error);
    
    await logSecurityEvent({
      type: 'SYSTEM',
      details: `Uncaught exception: ${error.message}`,
      severity: 'CRITICAL'
    });

    await shutdownApplication();
    process.exit(1);
  });

  process.on('unhandledRejection', async (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    
    await logSecurityEvent({
      type: 'SYSTEM',
      details: `Unhandled rejection: ${reason}`,
      severity: 'HIGH'
    });
  });
}

'use client';

import { useState, useEffect } from 'react';
import { useI18n } from '@/lib/i18n';
import { formatCurrency } from '@/lib/utils';
import { StatCard, Loading, Button } from '@/components/ui';
import { Users, FileText, Package, DollarSign, TrendingUp, Activity, Calendar, BarChart3 } from 'lucide-react';

export default function DashboardPage() {
    const { t, language } = useI18n();
    const [loading, setLoading] = useState(true);
    const [stats, setStats] = useState({
        customers: 0,
        invoices: 0,
        products: 0,
        revenue: 0,
    });

    // محاكاة تحميل البيانات
    useEffect(() => {
        const loadData = async () => {
            setLoading(true);
            // محاكاة API call
            await new Promise(resolve => setTimeout(resolve, 1500));
            setStats({
                customers: 124,
                invoices: 56,
                products: 89,
                revenue: 24500,
            });
            setLoading(false);
        };
        loadData();
    }, []);

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="space-y-2">
                    <div className="h-8 w-48 bg-muted rounded animate-pulse" />
                    <div className="h-4 w-32 bg-muted rounded animate-pulse" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {[...Array(4)].map((_, i) => (
                        <div key={i} className="h-32 bg-muted rounded-xl animate-pulse" />
                    ))}
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {[...Array(2)].map((_, i) => (
                        <div key={i} className="h-64 bg-muted rounded-xl animate-pulse" />
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-8 animate-fade-in">
            {/* Header */}
            <div className="space-y-2">
                <h1 className="text-3xl font-bold text-foreground heading">
                    {t('dashboard.title')}
                </h1>
                <p className="text-sm text-muted-foreground">
                    {language === 'ar' ? 'Dashboard' : 'لوحة التحكم'}
                </p>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                    title={t('dashboard.customers')}
                    value={stats.customers.toLocaleString()}
                    icon={<Users className="h-5 w-5" />}
                    color="blue"
                    trend={{
                        value: 12,
                        label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                        isPositive: true
                    }}
                    className="animate-fade-in-up animate-delay-100"
                />

                <StatCard
                    title={t('dashboard.invoices')}
                    value={stats.invoices.toLocaleString()}
                    icon={<FileText className="h-5 w-5" />}
                    color="green"
                    trend={{
                        value: 8,
                        label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                        isPositive: true
                    }}
                    className="animate-fade-in-up animate-delay-200"
                />

                <StatCard
                    title={t('dashboard.products')}
                    value={stats.products.toLocaleString()}
                    icon={<Package className="h-5 w-5" />}
                    color="purple"
                    trend={{
                        value: 5,
                        label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                        isPositive: true
                    }}
                    className="animate-fade-in-up animate-delay-300"
                />

                <StatCard
                    title={t('dashboard.revenue')}
                    value={formatCurrency(stats.revenue, language)}
                    icon={<DollarSign className="h-5 w-5" />}
                    color="orange"
                    trend={{
                        value: 15,
                        label: language === 'ar' ? 'مقارنة بالشهر الماضي' : 'vs last month',
                        isPositive: true
                    }}
                    className="animate-fade-in-up animate-delay-400"
                />
            </div>

            {/* Recent Activity Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-card rounded-xl shadow-sm border border-border overflow-hidden animate-fade-in-up animate-delay-500">
                    <div className="p-6 border-b border-border">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-xl font-semibold text-foreground heading">
                                    {t('dashboard.recentInvoices')}
                                </h2>
                                <p className="text-sm text-muted-foreground mt-1">
                                    {language === 'ar' ? 'Recent Invoices' : 'أحدث الفواتير'}
                                </p>
                            </div>
                            <Activity className="h-5 w-5 text-muted-foreground" />
                        </div>
                    </div>
                    <div className="p-8 text-center">
                        <div className="flex flex-col items-center space-y-3">
                            <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                                <FileText className="h-6 w-6 text-muted-foreground" />
                            </div>
                            <p className="text-muted-foreground">
                                {language === 'ar'
                                    ? 'سيتم عرض أحدث الفواتير هنا'
                                    : 'Recent invoices will be displayed here'}
                            </p>
                            <Button variant="outline" size="sm">
                                {language === 'ar' ? 'عرض الفواتير' : 'View Invoices'}
                            </Button>
                        </div>
                    </div>
                </div>

                <div className="bg-card rounded-xl shadow-sm border border-border overflow-hidden animate-fade-in-up animate-delay-600">
                    <div className="p-6 border-b border-border">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-xl font-semibold text-foreground heading">
                                    {t('dashboard.topProducts')}
                                </h2>
                                <p className="text-sm text-muted-foreground mt-1">
                                    {language === 'ar' ? 'Top Selling Products' : 'أكثر المنتجات مبيعًا'}
                                </p>
                            </div>
                            <TrendingUp className="h-5 w-5 text-muted-foreground" />
                        </div>
                    </div>
                    <div className="p-8 text-center">
                        <div className="flex flex-col items-center space-y-3">
                            <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                                <BarChart3 className="h-6 w-6 text-muted-foreground" />
                            </div>
                            <p className="text-muted-foreground">
                                {language === 'ar'
                                    ? 'سيتم عرض أكثر المنتجات مبيعًا هنا'
                                    : 'Top selling products will be displayed here'}
                            </p>
                            <Button variant="outline" size="sm">
                                {language === 'ar' ? 'عرض التقارير' : 'View Reports'}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-4 animate-fade-in-up animate-delay-700">
                <Button variant="gradient" leftIcon={<FileText className="h-4 w-4" />}>
                    {language === 'ar' ? 'إنشاء فاتورة جديدة' : 'Create New Invoice'}
                </Button>
                <Button variant="outline" leftIcon={<Users className="h-4 w-4" />}>
                    {language === 'ar' ? 'إضافة عميل' : 'Add Customer'}
                </Button>
                <Button variant="outline" leftIcon={<Package className="h-4 w-4" />}>
                    {language === 'ar' ? 'إضافة منتج' : 'Add Product'}
                </Button>
                <Button variant="ghost" leftIcon={<Calendar className="h-4 w-4" />}>
                    {language === 'ar' ? 'عرض التقويم' : 'View Calendar'}
                </Button>
            </div>
        </div>
    );
}

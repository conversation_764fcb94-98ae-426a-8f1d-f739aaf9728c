'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { TaxInvoiceTemplate } from '@/components/invoices/tax-invoice-template'
import Image from 'next/image'
import { TaxInvoiceQR } from '@/components/invoices/tax-invoice-qr'

export default function InvoicePrintPage() {
  const params = useParams()
  const [invoice, setInvoice] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  // معلومات الشركة
  const companyInfo = {
    name: 'شركة أمين بلس',
    nameEn: 'Amin Plus Company',
    address: 'دبي، الإمارات العربية المتحدة',
    addressEn: 'Dubai, United Arab Emirates',
    phone: '+971 4 123 4567',
    email: '<EMAIL>',
    website: 'www.aminplus.com',
    taxNumber: '123456789012345',
    logo: '/logo.png'
  }

  useEffect(() => {
    async function fetchInvoice() {
      try {
        const res = await fetch(`/api/invoices/${params.id}/export`)
        if (res.ok) {
          const data = await res.json()
          setInvoice(data)

          // طباعة الصفحة تلقائيًا بعد التحميل
          setTimeout(() => {
            window.print()
          }, 1000)
        } else {
          console.error('فشل جلب الفاتورة')
        }
      } catch (error) {
        console.error('خطأ:', error)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchInvoice()
    }
  }, [params.id])

  if (loading || !invoice) {
    return <div className="container py-10 text-center">جاري التحميل... / Loading...</div>
  }

  return (
    <div className="container py-6 max-w-4xl mx-auto print:shadow-none">
      <div className="print:hidden mb-4 flex justify-end space-x-4 rtl:space-x-reverse">
        <button
          onClick={() => window.print()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          طباعة الفاتورة / Print Invoice
        </button>
      </div>

      <TaxInvoiceTemplate
        invoice={invoice}
        showWatermark={true}
        showBackground={true}
        backgroundType="pattern"
        backgroundOpacity={0.05}
        watermarkText="فاتورة ضريبية | TAX INVOICE"
        watermarkOpacity={0.1}
        className="shadow-lg print:shadow-none"
      />

      {/* أنماط الطباعة - Print Styles */}
      <style dangerouslySetInnerHTML={{
        __html: `
        @media print {
          @page {
            size: A4;
            margin: 1cm;
          }
          body {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
            background-color: white;
          }
          .container {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            position: relative;
            overflow: hidden;
          }
          /* الحفاظ على الخلفية والعلامة المائية عند الطباعة */
          * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          /* إخفاء بعض العناصر عند الطباعة */
          .print\\:hidden {
            display: none !important;
          }
        }
        `
      }} />
    </div>
  )
}
    {invoice.customer.address && (
      <div className="flex justify-between border-b border-blue-100 pb-2">
        <div className="text-right">
          <span className="block font-medium text-gray-500">العنوان / Address</span>
          <span className="block">{invoice.customer.address}</span>
          <span className="block">{invoice.customer.addressEn || invoice.customer.address}</span>
        </div>
      </div>
    )}

    {(invoice.customer.phone || invoice.customer.email) && (
      <div className="flex justify-between border-b border-blue-100 pb-2">
        <div className="text-right">
          <span className="block font-medium text-gray-500">الاتصال / Contact</span>
          {invoice.customer.phone && <span className="block">هاتف / Tel: {invoice.customer.phone}</span>}
          {invoice.customer.email && <span className="block">البريد / Email: {invoice.customer.email}</span>}
        </div>
      </div>
    )}

    {invoice.customer.taxNumber && (
      <div className="flex justify-between pt-2">
        <div className="text-right">
          <span className="block font-medium text-gray-500">الرقم الضريبي / Tax Number</span>
          <span className="block font-bold text-blue-800">{invoice.customer.taxNumber}</span>
        </div>
      </div>
    )}

  {/* تفاصيل الفاتورة - Invoice Details */}
  <div className="grid grid-cols-2 gap-8 mb-8">
    <div className="border p-4 rounded-md">
      <h2 className="text-lg font-bold mb-2 flex justify-between">
        <span>تفاصيل الفاتورة:</span>
        <span>Invoice Details:</span>
      </h2>
      <div className="flex justify-between mb-1">
        <span>تاريخ الإصدار: {issueDateFormatted.ar}</span>
        <span>Issue Date: {issueDateFormatted.en}</span>
      </div>
      <div className="flex justify-between mb-1">
        <span>تاريخ الاستحقاق: {dueDateFormatted.ar}</span>
        <span>Due Date: {dueDateFormatted.en}</span>
      </div>
    </div>
    <div className="border p-4 rounded-md">
      <h2 className="text-lg font-bold mb-2 flex justify-between">
        <span>الحالة:</span>
        <span>Status:</span>
      </h2>
      <div className="flex justify-between mb-1">
        <span>{statusText.ar}</span>
        <span>{statusText.en}</span>
      </div>
      <div className="flex justify-between mb-1">
        <span>طريقة الدفع: تحويل بنكي</span>
        <span>Payment Method: Bank Transfer</span>
      </div>
    </div>
  </div>

  {/* جدول المنتجات - Products Table */}
  <div className="mb-8 relative z-10">
    <div className="bg-blue-800 text-white py-3 px-4 rounded-t-lg flex justify-between items-center">
      <h3 className="font-bold">تفاصيل المنتجات / Product Details</h3>
      <div className="text-sm">عدد المنتجات: {invoice.items.length}</div>
    </div>
    <div className="border-x border-b rounded-b-lg overflow-hidden">
      <table className="w-full border-collapse">
        <thead>
          <tr className="bg-gradient-to-r from-blue-50 to-blue-100 text-blue-800">
            <th className="p-3 text-right border-b border-blue-200 w-12">#</th>
            <th className="p-3 text-right border-b border-blue-200">
              <div className="flex justify-between">
                <span>الوصف</span>
                <span>Description</span>
              </div>
            </th>
            <th className="p-3 text-right border-b border-blue-200 w-20">
              <div className="flex justify-between">
                <span>الكمية</span>
                <span>Qty</span>
              </div>
            </th>
            <th className="p-3 text-right border-b border-blue-200 w-32">
              <div className="flex justify-between">
                <span>سعر الوحدة</span>
                <span>Unit Price</span>
              </div>
            </th>
            <th className="p-3 text-right border-b border-blue-200 w-32">
              <div className="flex justify-between">
                <span>الضريبة</span>
                <span>VAT</span>
              </div>
            </th>
            <th className="p-3 text-right border-b border-blue-200 w-32">
              <div className="flex justify-between">
                <span>الإجمالي</span>
                <span>Total</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {invoice.items.map((item: any, index: number) => {
            const itemTotal = item.quantity * item.unitPrice;
            const itemTax = itemTotal * (invoice.taxRate / 100);
            const itemTotalWithTax = itemTotal + itemTax;

            return (
              <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-blue-50'}>
                <td className="p-3 text-right border-b border-blue-100 font-medium">{index + 1}</td>
                <td className="p-3 text-right border-b border-blue-100">
                  <div className="flex flex-col">
                    <span className="font-medium">{item.description || item.product?.name || 'منتج'}</span>
                    <span className="text-sm text-gray-500">{item.descriptionEn || item.product?.nameEn || 'Product'}</span>
                  </div>
                </td>
                <td className="p-3 text-right border-b border-blue-100">{item.quantity}</td>
                <td className="p-3 text-right border-b border-blue-100">{item.formattedUnitPrice || formatCurrency(item.unitPrice)}</td>
                <td className="p-3 text-right border-b border-blue-100">
                  <div className="flex flex-col">
                    <span>{formatCurrency(itemTax)}</span>
                    <span className="text-xs text-gray-500">({invoice.taxRate}%)</span>
                  </div>
                </td>
                <td className="p-3 text-right border-b border-blue-100 font-bold text-blue-800">{item.formattedTotal || formatCurrency(itemTotalWithTax)}</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  </div>

  {/* ملخص الفاتورة - Invoice Summary */}
  <div className="grid grid-cols-5 gap-8 mb-8 relative z-10">
    <div className="col-span-3">
      {/* المبلغ بالحروف - Amount in Words */}
      <div className="bg-gradient-to-br from-white to-blue-50 border border-blue-100 p-6 rounded-lg shadow-sm h-full">
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
            </svg>
          </div>
          <h2 className="text-lg font-bold text-blue-800">
            <span className="ml-2">المبلغ بالحروف</span>
            <span className="text-sm text-gray-500 block">Amount in Words</span>
          </h2>
        </div>

        <div className="space-y-4 text-sm">
          <div className="border-b border-blue-100 pb-3">
            <span className="block font-medium text-gray-500 mb-1">بالعربية / In Arabic</span>
            <span className="block font-bold text-blue-800">فقط {invoice.amountInWords?.ar || 'مبلغ وقدره'} لا غير</span>
          </div>

          <div>
            <span className="block font-medium text-gray-500 mb-1">بالإنجليزية / In English</span>
            <span className="block font-bold text-blue-800">Only {invoice.amountInWords?.en || 'amount of'} only</span>
          </div>
        </div>
      </div>
    </div>

    <div className="col-span-2">
      <div className="bg-white border border-blue-200 rounded-lg overflow-hidden shadow-sm">
        <div className="bg-blue-800 text-white py-3 px-4 flex justify-between items-center">
          <h3 className="font-bold">ملخص الفاتورة</h3>
          <h3 className="font-bold">Invoice Summary</h3>
        </div>

        <div className="divide-y divide-blue-100">
          <div className="flex justify-between p-4">
            <div className="flex justify-between w-full">
              <span className="text-gray-600">المجموع الفرعي:</span>
              <span className="text-gray-600">Subtotal:</span>
            </div>
            <span className="font-medium text-blue-800 mr-4 rtl:ml-4 rtl:mr-0">{invoice.formattedSubtotal || formatCurrency(invoice.subtotal)}</span>
          </div>

          <div className="flex justify-between p-4">
            <div className="flex justify-between w-full">
              <span className="text-gray-600">الضريبة ({invoice.taxRate}%):</span>
              <span className="text-gray-600">VAT ({invoice.taxRate}%):</span>
            </div>
            <span className="font-medium text-blue-800 mr-4 rtl:ml-4 rtl:mr-0">{invoice.formattedTaxAmount || formatCurrency(invoice.taxAmount)}</span>
          </div>

          {invoice.discount > 0 && (
            <div className="flex justify-between p-4">
              <div className="flex justify-between w-full">
                <span className="text-gray-600">الخصم ({invoice.discount}%):</span>
                <span className="text-gray-600">Discount ({invoice.discount}%):</span>
              </div>
              <span className="font-medium text-red-600 mr-4 rtl:ml-4 rtl:mr-0">-{formatCurrency(invoice.subtotal * invoice.discount / 100)}</span>
            </div>
          )}

          <div className="flex justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100">
            <div className="flex justify-between w-full">
              <span className="font-bold text-blue-800">الإجمالي:</span>
              <span className="font-bold text-blue-800">Total:</span>
            </div>
            <span className="font-bold text-blue-800 text-lg mr-4 rtl:ml-4 rtl:mr-0">{invoice.formattedTotal || formatCurrency(invoice.total)}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  {/* الشروط والملاحظات - Terms & Notes */}
{
  (invoice.notes || invoice.terms) && (
    <div className="grid grid-cols-2 gap-8 mb-8 relative z-10">
      {invoice.terms && (
        <div className="bg-gradient-to-br from-white to-blue-50 border border-blue-100 p-6 rounded-lg shadow-sm">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 4h3a3 3 0 006 0h3a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm2.5 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm2.45 4a2.5 2.5 0 10-4.9 0h4.9zM12 9a1 1 0 100 2h3a1 1 0 100-2h-3zm-1 4a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-lg font-bold text-blue-800">
              <span className="ml-2">الشروط والأحكام</span>
              <span className="text-sm text-gray-500 block">Terms & Conditions</span>
            </h2>
          </div>
          <div className="text-sm text-gray-700 leading-relaxed">
            {invoice.terms}
          </div>
        </div>
      )}

      {invoice.notes && (
        <div className="bg-gradient-to-br from-white to-blue-50 border border-blue-100 p-6 rounded-lg shadow-sm">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-lg font-bold text-blue-800">
              <span className="ml-2">ملاحظات</span>
              <span className="text-sm text-gray-500 block">Notes</span>
            </h2>
          </div>
          <div className="text-sm text-gray-700 leading-relaxed">
            {invoice.notes}
          </div>
        </div>
      )}
    </div>
  )
}

{/* التوقيع - Signature */}
<div className="grid grid-cols-2 gap-8 mb-8 relative z-10">
  <div className="bg-white border border-blue-100 p-6 rounded-lg shadow-sm">
    <div className="flex items-center mb-4">
      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
          <path fillRule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clipRule="evenodd" />
        </svg>
      </div>
      <h2 className="text-lg font-bold text-blue-800">
        <span className="ml-2">توقيع المستلم</span>
        <span className="text-sm text-gray-500 block">Receiver's Signature</span>
      </h2>
    </div>
    <div className="h-20 border-b border-blue-200 mt-4 mb-2"></div>
    <div className="text-sm text-gray-500 text-center">
      الاسم / Name: ____________________________
    </div>
  </div>

  <div className="bg-white border border-blue-100 p-6 rounded-lg shadow-sm">
    <div className="flex items-center mb-4">
      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
        </svg>
      </div>
      <h2 className="text-lg font-bold text-blue-800">
        <span className="ml-2">ختم الشركة</span>
        <span className="text-sm text-gray-500 block">Company Stamp</span>
      </h2>
    </div>
    <div className="h-20 border border-dashed border-blue-200 rounded-lg mt-4 mb-2 flex items-center justify-center">
      <div className="text-sm text-gray-400">ختم الشركة / Company Stamp</div>
    </div>
    <div className="text-sm text-gray-500 text-center">
      التاريخ / Date: ____________________________
    </div>
  </div>
</div>

{/* تذييل الفاتورة - Footer */}
<div className="mt-8 text-center relative z-10">
  <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-6 px-4 rounded-lg">
    <div className="mb-2 text-lg font-bold">
      شكراً لتعاملكم معنا | Thank you for your business
    </div>
    <div className="text-sm opacity-80">
      تم إنشاء هذه الفاتورة بواسطة نظام أمين بلس | This invoice was generated by Amin Plus | أمين بلس System
    </div>
  </div>
</div>

{/* QR Code للفاتورة الضريبية */}
<div className="absolute top-4 left-4 w-32 h-32 bg-white rounded-lg shadow-md p-2 flex flex-col items-center justify-center">
  <div className="w-24 h-24 border border-blue-200 rounded-md p-1 bg-white mb-1">
    <TaxInvoiceQR
      sellerName={companyInfo.name}
      sellerTaxNumber={companyInfo.taxNumber}
      invoiceTimestamp={invoice.issueDate}
      invoiceTotal={invoice.total || 0}
      taxAmount={invoice.taxAmount || 0}
    />
  </div>
  <div className="text-xs text-center text-blue-800 font-medium">
    رمز QR للفاتورة الضريبية
    <br />
    Tax Invoice QR Code
  </div>
</div>

{/* أنماط الطباعة - Print Styles */}
<style dangerouslySetInnerHTML={{
  __html: `
        @media print {
          @page {
            size: A4;
            margin: 1cm;
          }
          body {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
            background-color: white;
          }
          .container {
            width: 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
            position: relative;
            overflow: hidden;
          }
          /* الحفاظ على الخلفية والعلامة المائية عند الطباعة */
          * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          /* تحسين مظهر الحدود عند الطباعة */
          .border {
            border: 1px solid #ddd !important;
          }
          .border-b {
            border-bottom: 1px solid #ddd !important;
          }
          .border-t {
            border-top: 1px solid #ddd !important;
          }
          /* تحسين مظهر الخلفيات عند الطباعة */
          .bg-gray-50, .bg-gray-100 {
            background-color: #f9fafb !important;
          }
          /* إخفاء بعض العناصر عند الطباعة */
          .print\\:hidden {
            display: none !important;
          }
        }
        `
}} />
    </div>
  )
}

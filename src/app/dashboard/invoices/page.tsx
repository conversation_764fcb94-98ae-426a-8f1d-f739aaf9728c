'use client';

import { Suspense } from 'react';
import Link from 'next/link';
import { PlusCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import InvoicesList from '@/components/invoices/invoices-list';
import { useI18n } from '@/lib/i18n';

export default function InvoicesPage() {
    const { t, language } = useI18n();

    return (
        <div className="p-6">
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h1 className="text-2xl font-semibold mb-1">{t('invoices.title')}</h1>
                    <p className="text-sm text-gray-500">
                        {language === 'ar' ? 'Invoices Management' : 'إدارة الفواتير'}
                    </p>
                </div>
                <Link href="/dashboard/invoices/new">
                    <Button>
                        <PlusCircle className="h-4 w-4 ml-2" />
                        {t('invoices.create')}
                    </Button>
                </Link>
            </div>

            <Suspense fallback={
                <div className="text-center py-8">
                    {t('common.loading')}
                </div>
            }>
                <InvoicesList />
            </Suspense>
        </div>
    );
}

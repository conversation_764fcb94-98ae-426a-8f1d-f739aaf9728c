'use client';

import { Suspense, useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusCircle, FileText, DollarSign, Clock, CheckCircle, Search, Filter } from 'lucide-react';
import { Button, StatCard, Loading, Input } from '@/components/ui';
import InvoicesList from '@/components/invoices/invoices-list';
import { useI18n } from '@/lib/i18n';
import { formatCurrency } from '@/lib/utils';

export default function InvoicesPage() {
    const { t, language } = useI18n();
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [stats, setStats] = useState({
        total: 0,
        pending: 0,
        paid: 0,
        overdue: 0,
        totalAmount: 0
    });

    // محاكاة تحميل البيانات
    useEffect(() => {
        const loadStats = async () => {
            setLoading(true);
            await new Promise(resolve => setTimeout(resolve, 1000));
            setStats({
                total: 156,
                pending: 23,
                paid: 128,
                overdue: 5,
                totalAmount: 125000
            });
            setLoading(false);
        };
        loadStats();
    }, []);

    if (loading) {
        return <Loading />;
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                    <h1 className="text-3xl font-bold text-foreground heading">
                        {t('invoices.title')}
                    </h1>
                    <p className="text-sm text-muted-foreground mt-1">
                        {language === 'ar' ? 'Invoices Management' : 'إدارة الفواتير'}
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        {language === 'ar' ? 'تصفية' : 'Filter'}
                    </Button>
                    <Link href="/dashboard/invoices/new">
                        <Button variant="gradient">
                            <PlusCircle className="h-4 w-4 mr-2" />
                            {t('invoices.create')}
                        </Button>
                    </Link>
                </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <StatCard
                    title={language === 'ar' ? 'إجمالي الفواتير' : 'Total Invoices'}
                    value={stats.total.toLocaleString()}
                    icon={<FileText className="h-5 w-5" />}
                    color="blue"
                />
                <StatCard
                    title={language === 'ar' ? 'في الانتظار' : 'Pending'}
                    value={stats.pending.toLocaleString()}
                    icon={<Clock className="h-5 w-5" />}
                    color="yellow"
                />
                <StatCard
                    title={language === 'ar' ? 'مدفوعة' : 'Paid'}
                    value={stats.paid.toLocaleString()}
                    icon={<CheckCircle className="h-5 w-5" />}
                    color="green"
                />
                <StatCard
                    title={language === 'ar' ? 'متأخرة' : 'Overdue'}
                    value={stats.overdue.toLocaleString()}
                    icon={<Clock className="h-5 w-5" />}
                    color="red"
                />
                <StatCard
                    title={language === 'ar' ? 'إجمالي المبلغ' : 'Total Amount'}
                    value={formatCurrency(stats.totalAmount, language)}
                    icon={<DollarSign className="h-5 w-5" />}
                    color="purple"
                />
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder={language === 'ar' ? 'البحث في الفواتير...' : 'Search invoices...'}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
            </div>

            {/* Invoices List */}
            <div className="bg-card rounded-xl border border-border overflow-hidden">
                <Suspense fallback={
                    <div className="p-8 text-center">
                        <Loading />
                    </div>
                }>
                    <InvoicesList searchTerm={searchTerm} />
                </Suspense>
            </div>
        </div>
    );
}

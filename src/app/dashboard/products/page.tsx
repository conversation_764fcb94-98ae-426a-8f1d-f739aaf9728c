'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Button } from '@/components/ui'
import { useRouter } from 'next/navigation'
// import { usePermission } from '@/hooks/usePermission'
import { Edit, Trash2, Eye, PlusCircle } from 'lucide-react'
import { Breadcrumb } from '@/components/ui/breadcrumb'
import { DataTable } from '@/components/ui/data-table'
import { toast } from 'sonner'

interface Product {
    id: string
    name: string
    price: number
    sku: string
    unit: string
    quantity?: number
    category?: string
    cost?: number
    createdAt?: Date
}

export default function ProductsPage() {
    const [products, setProducts] = useState<Product[]>([])
    const [loading, setLoading] = useState(true)
    const router = useRouter()
    const canManageProducts = true // تم تعطيل التحقق من الصلاحيات مؤقتًا

    useEffect(() => {
        async function fetchProducts() {
            try {
                const response = await fetch('/api/products');
                if (!response.ok) {
                    throw new Error('Failed to fetch products');
                }
                const data = await response.json();
                setProducts(data.products || []);
                setLoading(false);
            } catch (error) {
                console.error('خطأ في جلب بيانات المنتجات:', error);
                setLoading(false);
                toast.error('حدث خطأ أثناء تحميل المنتجات');
            }
        }

        fetchProducts()
    }, [])

    const handleAddProduct = () => {
        router.push('/dashboard/products/new')
    }

    const handleEditProduct = (id: string) => {
        router.push(`/dashboard/products/${id}/edit`)
    }

    const handleDeleteProduct = async (id: string) => {
        if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            try {
                const response = await fetch(`/api/products/${id}`, {
                    method: 'DELETE',
                });

                if (!response.ok) {
                    throw new Error('Failed to delete product');
                }

                setProducts(products.filter(product => product.id !== id));
                toast.success('تم حذف المنتج بنجاح');
            } catch (error) {
                console.error('Error deleting product:', error);
                toast.error('حدث خطأ أثناء حذف المنتج');
            }
        }
    }

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('ar-AE', { style: 'currency', currency: 'AED' }).format(price)
    }

    // تعريف أعمدة جدول المنتجات
    const columns = [
        {
            key: 'name',
            header: 'اسم المنتج | Product Name',
            cell: (product: Product) => <span className="font-medium">{product.name}</span>,
            sortable: true,
        },
        {
            key: 'sku',
            header: 'رمز المنتج | SKU',
            cell: (product: Product) => <span>{product.sku}</span>,
            sortable: true,
        },
        {
            key: 'unit',
            header: 'الوحدة | Unit',
            cell: (product: Product) => <span>{product.unit}</span>,
            sortable: true,
        },
        {
            key: 'price',
            header: 'سعر البيع | Price',
            cell: (product: Product) => <span>{formatPrice(product.price)}</span>,
            sortable: true,
        },
        {
            key: 'quantity',
            header: 'الكمية | Quantity',
            cell: (product: Product) => (
                <span
                    className={
                        product.quantity && product.quantity <= 5
                            ? 'text-red-500 font-medium'
                            : product.quantity && product.quantity <= 10
                                ? 'text-amber-500 font-medium'
                                : ''
                    }
                >
                    {product.quantity || '-'}
                </span>
            ),
            sortable: true,
        },
        {
            key: 'category',
            header: 'التصنيف | Category',
            cell: (product: Product) => <span>{product.category || '-'}</span>,
            sortable: true,
        },
        {
            key: 'actions',
            header: 'الإجراءات | Actions',
            cell: (product: Product) => (
                <div className="flex items-center gap-2">
                    <Button
                        variant="ghost"
                        size="icon"
                        title="عرض | View"
                        onClick={() => router.push(`/dashboard/products/${product.id}`)}
                    >
                        <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        title="تعديل | Edit"
                        onClick={() => handleEditProduct(product.id)}
                    >
                        <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        title="حذف | Delete"
                        onClick={() => handleDeleteProduct(product.id)}
                    >
                        <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                </div>
            ),
        },
    ];

    return (
        <div className="space-y-6">
            <Breadcrumb
                segments={[
                    { title: 'الرئيسية', href: '/dashboard' },
                    { title: 'المنتجات', href: '/dashboard/products' },
                ]}
                className="mb-4"
            />

            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold mb-1">إدارة المنتجات</h1>
                    <p className="text-sm text-gray-500">Products Management</p>
                </div>
                {canManageProducts && (
                    <Button onClick={handleAddProduct}>
                        <PlusCircle className="ml-2 h-4 w-4" /> إضافة منتج جديد | New Product
                    </Button>
                )}
            </div>

            <Card>
                <CardHeader>
                    <div>
                        <CardTitle>قائمة المنتجات</CardTitle>
                        <p className="text-sm text-gray-500 mt-1">Products List</p>
                    </div>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex justify-center p-4">
                            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        </div>
                    ) : products.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                            لا توجد منتجات حتى الآن
                            <br />
                            <span className="text-sm">No products found</span>
                        </div>
                    ) : (
                        <DataTable
                            data={products}
                            columns={columns}
                            searchable
                            searchKeys={['name', 'sku', 'category']}
                            pagination
                            pageSize={10}
                        />
                    )}
                </CardContent>
            </Card>
        </div>
    )
}

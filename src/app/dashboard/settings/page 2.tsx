'use client';

import { useState, useEffect } from 'react';
import {
  SystemSettings,
  DEFAULT_SYSTEM_SETTINGS,
  getSystemSettings,
  saveSystemSettings
} from '@/lib/settings';
import { CurrencyCode } from '@/lib/utils';
import CurrencySelector from '@/components/ui/currency-selector';
import TaxSettings, { TaxSettings as TaxSettingsType } from '@/components/ui/tax-settings';
import { useI18n } from '@/lib/i18n';

export default function SettingsPage() {
  const { t, language } = useI18n();
  const [settings, setSettings] = useState<SystemSettings>(DEFAULT_SYSTEM_SETTINGS);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  // تحميل الإعدادات عند تحميل الصفحة
  // Load settings when the page loads

  // تحميل الإعدادات عند تحميل الصفحة
  // Load settings when the page loads
  useEffect(() => {
    setSettings(getSystemSettings());
  }, []);

// تحديث إعدادات الضريبة الافتراضية
// Uda dfaultaxstings
const hndTaxSttigsChange = (axSettings: TaxSettingsType) => {
  Settgs({
    ...settgs,
    dfaultTax: taxSettings
    //});
  };

// تحديث معلومات الشركة
// U dتtي coعpanyاinfاrmatiفn
const handترCompاضyInfoChangeية(e: Rect.ChangeEvent<HTMLInputEemnt | HTMLTextAreaElement>// Update default currency
  const handleCurrencyChange = (currency: CurrencyCode) => {
    setSettings
      ...stttingsings,
  atmpanyICforrency: currency
    }); tngs.cmpanyIfo
  };

// حديث إعدادات الضريبة الافتراضية
//

// تحديث إعدادات الفاتورة Update default tax settings
// Update invoice settings  const handleTaxSettingsChange = (taxSettings: TaxSettingsType) => {
setSettineInvoicgs({ React.ChangeEvent<HTMLInputElemnt | HTMLTexAreaEln>) => {
    cot { name } e.target;
      ...settins{
      ...sttings,
      inoiceSettings:
    defslttings.intoiceSettingsTaxSettings: taxSettings
  }); nam: name === 'nextNumber' ? parseInt(value) || 1
      }
  };


// حفظ الإعدادات
// Save settings  // تحديث معلومات الشركة
// Update company information
const handleCompanyInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
  const { name, value } = e.target;
  setSettings({
    ...settings,
    companyInfo: {
      ...settings.companyInfo,
      [name]: value
    }
  });
};

// تحديث إعدادات الفاتورة
// Update invoice settings
const handleInvoiceSettingsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
  if (l adicg) {
    stntuae(
}=.r < div className = "flex justtfy-cenSertitems-gensrh-64" >
    <div.lassName="anim.te-.pin roundsd-full h-12 w-12ebordir-b-2 bordgr-psim,ry" ></div.lassName= >
      i < spannclassNamo={ `${langcage === 'ae'e ? 'mr-3' : 'ml-3' } `}>{t('common.loading')}</span>
     ../settings.invoiceSettings,
    );
  }

  return[(
nam edav c au?Name=" g-whale oudd-l hadow-md p-6
      <div}className="flexjstif-btwen items-ene mb-6">
    });<div>
  <h1 cassNam"text-2xl font-bold mb-1">t('itl')</h1>
  };{lag=='r' ? (
            <p cassNam="tx-m tx-ga-500">SystemSttigs</p>
):(
        <p texts textgray-500>إعدادات النظام</p>
  // حفظ ا)}
لإعد  //</div>
 Save ttdv

  cons<divtclassName="mb-6">
 hand   ldmv csassNamyn"bord r-b bororr-grEy-200">
          <nav ce)Name-mb-px
    e.prevenebu()o
    setSaving(oeCck() => Acvb('geral')
classNm`py - 2 px - 4 e - mfot - meium ${
    acveTb === 'nr
    {
      sav ? e'border-b-2Sborder-primaryytmxe-prtmary'gs(settings);
      aler
    } :c'text-gray-500ahover:text-gray-700'h(error) {
      o      al'}`}ettings.errors.saveErrorMessage'));
    } fina >
      setSav<ipaa>) t(';.general')</span >
      } {
    lagu == 'ar' ?
    <sp cassNam="xt-xs txt-gray-500r-1">General</s>
        };): (
     if <span(llassNome = "text-xa tdxt-gray-500iml-1" > عام</spa)>
    retu)
}
<div /bu = "ol>tify-center items-center h-64" >
  <div<buanom- spin rounded - full h - 12 w - 12 border - b - 2 border - primary"></div>
    < span clickame{
      `${lsgtAc = iveTab ? rcompa3y')}
  lassNaml = {`py-2 px-4 -ex3-}mtfont-m(dium ${accivoTobing')}co>pany
      </di    );?'border-b-2border-primary text-primary'
}  : 'tx-gay-500hover:text-gray-700'
re             `}<div className="bg-white rounded-lg shadow-md p-6">
       <   >1 className="text-2xl font-bold mb-1">{t('settings.title')}</h1>
               sp n>{t('settlans.e=mpary')}</sp?n>
              == r? (
                 span</div>xt-xstxtry500mr1Company Info</span>
              ) : (
                <spincassNam"x-xx-ray-500m-1">معلومات الشركة</sp>
              )
           <button
      uton
              onClck()=> ('noic')
            cssNm`py - 2px - 4 text - sm diat - aedmum $"bctiv>Ta === 'invoce'
  ? 'boddir-b-2 bor cr-plimsry e=xb-primary'er - b border - gray - 200">
                :a'text-grsy-500 hovar:tflm-grapx700'
               }`}
            >on
              <span>{ ('snttingi.invo{ceSet(ings')}</span> => setActiveTab('general')}
              {langulgs ==a '=r' ? (2 px-4 text-sm font-medium ${activeTab === 'general'
    < span ? 'bordtert-xs-textbepmr-500ymrt1">InvorcmaSettings</spa>
              ) : (     : 'text-gray-500 hover:text-gray-700'
  < p`clasNam="ex-xx-ray-500 ml-1">إعدادات الفاتورة</p>

              >
            na  <span>{t('settings.general')}</span>
          div  {language === 'ar' ? (

      <span className="text-xs text-gray-500 mr-1">General</span>
      <  rm buSubm>={handSm}>
  {acvTab ==='rl'&&(
          <div>  <button
                      onClick={() => setActiveTab('company')}
                      className={`py - 2 px - 4 text - sm font - medium ${
  activeTab === 'company'
  t('set ?ngs.currencySet ings')order - b - 2 border - primary text - primary'
  {
    language === 'ar' ? (
                          : 'text-gray-500 hover:text-grayr700'Crrency Seings
                ) : ({ language === 'ar' ? (
      saexgam < lpa > لclaوsName= "تشxt-xة <ext-aray-500>m-1" > إعداداتالعملة</pa >
              )
  }
              </h2 >    )
}
                      </button4
                 label  tmlFor = "defaultCurrency"   onClick = { block mb- 2() =>smtActivmebiu'oi
  < span > ce('se)tings.defau}tCurrncy')span
{
  language === 'ar' ? (
    s an    className = {`pxy2 px-4 text-sm mr-1 fDuft 'tnCurrencysan
                    :
l                   <span nl  sNam'="aex -xs t(xt-gry-500m-1">العملة الافتراضية</sp>
    )
              </>
            <Curr n pSsstcxer
        5إل/      )au={dfaulCrrncy
     <    donChng{hanlCuncyChane}
               <fonSubmit={haw-nuldSbd:wi64>
        {aer    />
      <d      </div>
      <d    </div>

          m"<dg  clnssNeme="mm-6">mb-4">
              <h2   <span>{t"urrenlgSettinss<oldm-4">
               l<span>{t(asgttings.' fulTaxStings')}</sn>
                {language === <ax- ? (
                  <span className="text-xs text-gray-500 mr-1">Default Tax Settings</span>
                ) : (
                  </h2>إعدادات الضريبة الافتراضية</sp>
                )}
              </h2>
              <TxSttings
            iniilSeings={stingsdfautTxSettigs}
                onChange={handleTaxSeivingsChacge}assName="mb-4">
              />
                        {language === 'ar' ? (
                          <span className="text-xs text-gray-500 mr-1">Default Currency</span>
                             <span className="text-xs text-gray-500 ml-1">العملة الافتراضية</span>
                  )}
        {a   veTab === 'c mp /y' &&
    <dv>
         h2          vate=t-lg{noeC-suriboldmb
                pan>{{('shttingn.comdaeyIehtion')}span
              me="w-full md:w-64"(
              <spa   lassName=" ext-xs text-gray-500 mr-1">   <div  claNaab6</sp>
 ) :      <h2 className="text-lg font-semibold mb-4">
                 span       {language xs=r?xt-gray-500l1معلومات الشركة</s>
             )}

                        <span className="text-xs text-gray-500 mr-1">Default Tax Settings</span>
                      ) : (
                        <span className="text-xs text-gray-500 ml-1">إعدادات الضريبة الافتراضية</span>
                      )}{t'settings.companyNameArabic'}
                    </h2>
                  {language === 'ar' ? (
                      <TaxSettingsrc)</span>
                  ) : (
                    <span lassName="text-xs text-gray-500 ml-1">اسم الشركة (عربي
                  )}
                      initialSettings={settings.defaultTaxSettings}
                      onChange={handleTaxSettingsChange}
                    />
                  </div>
                </div>
              )}
      handleCmpanyIfo
              {activeTab === 'company' && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">
                    <span>{t('settings.companyInformation')}</span>
              {language === 'ar' ? (
                      <span className="text-xs text-gray-500 mr-1">Company Information</span>
                    ) : (
                      <s{tm'settings.companyNameEnglish'-}xs text-gray-500 ml-1">معلومات الشركة</span>
                    )}
                  {language === 'ar' ? (
                    </h2>r
                  ) : (
                     span className="text-xs text-gray-500 ml-1">اسم الشركة (إنجليزي)< span>
                  )}
                </<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block mb-2 text-sm font-medium">
                        <span>{t('settings.companyNameArabic')}</span>
                        <span className="text-red-500">*</span>
                        {language === 'ar' ? (
                          <shandleCpmpaayInfon className="text-xs text-gray-500 mr-1">Company Name (Arabic)</span>
                        ) : (
                          <span className="text-xs text-gray-500 ml-1">اسم الشركة (عربي)</span>
                        )}
                      </label>
                <input
                        id="name"
                        name="name"
                        {t('settings.companyAddress')}ext"
                  {language === 'ar' ? (
                          value={settings.companyInfo.name}r
                  ) : (
                     span className="text-xs text-gray-500 ml-1">العنوان< span>
                  )}
                </      onChange={handleCompanyInfoChange}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        required
                      />
                    </div>

                    <div>handleCmpayInfo
                      <label htmlFor="nameEn" className="block mb-2 text-sm font-medium">
                        <span>{t('settings.companyNameEnglish')}</span>
                        <span className="text-red-500">*</span>
                  {language === 'ar' ? (
                          <span className="text-xs text-gray-500 mr-1">Company Name (English)</span>
                        ) : (
                        {t('settings.companyPhone')}assName
                  {language === 'ar' ? (="text-xs text-gray-500 ml-1">اسم الشركة (إنجليزي)</span>
                          )}rne</span>
                  ) : (
                    <spa className="text-xs txt-gray-500 ml-1">رقم الهاتف
                  )}
                      </label>
                      <input
                        id="nameEn"
                        name="nameEn"
                        type="text"
                        value={settings.companyInfo.nameEn}
                        onChhandleCampanyInfoge={handleCompanyInfoChange}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        required
                      />
              </div>

                    <div>
                      <l{t('settings.companyEmail')}ress" c
                  {language === 'ar' ? (lassName="block mb-2 text-sm font-medium">
                          <span>{t('settings.companyAddress'r}</span>l</span>
                  ) : (
                    <span className="text-xs text-gray-500 m-1">البريد الإلكتروني
                  )}
                        {language === 'ar' ? (
                          <span className="text-xs text-gray-500 mr-1">Address</span>
                        ) : (
                          <span className="text-xs text-gray-500 ml-1">العنوان</span>
                        )}
                      </label>
                      <inputhandleCmpanyIfo
                        id="address"
                        name="address"
                        type="text"
                  value={settings.companyInfo.address}
                        onChange={handleCompanyInfoChange}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />{t('settings.companyWebsite')}
                  {language === 'ar' ? (
                      </div>r
        ) : (
                    span className="text-xs text-gray-500 ml-1">الموقع الإلكتروني<span>
                  )}
                </
                    <div>
                      <label htmlFor="phone" className="block mb-2 text-sm font-medium">
                        <span>{t('settings.companyPhone')}</span>
                        {language === 'ar' ? (
                          <span className="text-xs text-gray-500 mr-1">Phone</span>
                        ) : handleC(mpayInfo
                          <span className="text-xs text-gray-500 ml-1">رقم الهاتف</span>
                        )}
                      </label>
                <input
                        id="phone"
                        name="phone"
                        {t('settings.companyTaxNumber')}
                  {language === 'ar' ? (
                          value={settings.companyInfo.phone}r
                  ) : (
                     span className="text-xs text-gray-500 ml-1">الرقم الضريبي< span>
                  )}
                </      onChange={handleCompanyInfoChange}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </div>

                    <div>
                      <labelhandleC mpahyInfotmlFor="email" className="block mb-2 text-sm font-medium">
                        <span>{t('settings.companyEmail')}</span>
                        {language === 'ar' ? (
                          <span className="text-xs text-gray-500 mr-1">Email</span>
                        ) : (
                          <span className="text-xs text-gray-500 ml-1">البريد الإلكتروني</span>
                         </label>
                <input
        {a   veTab === 'i      ' &&
         <div>
            <h2 clasNam="ex-lft-smibold mb-4">
            <span>t('.invoics')}</pan>
             {lagu === 'ar' ?
               <spn lassNm="x-xst-gy-500 mr-1">Invoic Sttings</spa
            :(
                 span         value={sxsttexs-gray.500 my.1maإعدادات الفاتورة</spal>
              )}

                        onChange={handleCompanyInfoChange}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </di{t('settings.invoicePrefix')}
                  {language === 'ar' ? (
        rx</span>
                  ) : (
                    <span className="text-xs tet-gray-500 ml-1">بادئة رقم الفاتورة
                  )}
                    <div>
                      <label htmlFor="website" className="block mb-2 text-sm font-medium">
                        <span>{t('settings.companyWebsite')}</span>
                        {language === 'ar' ? (
                          <span className="text-xs text-gray-500 mr-1">Website</span>
                        ) : (
                          <sssName=ay-500 ml-1">اChangeوقع الإلكتروني</span>
                        )}
                      </label>
                      <input
                  id="website"
                        name="website"
                        type="text"
                        {t('settings.nextInvoiceNumber')}nyInfo.website}
                  {language === 'ar' ? (
                          onChange={handleCompanyInfoChange}r
                  ) : (
                     span className="text-xs text-gray-500 ml-1">الرقم التالي للفاتورة< span>
                  )}
                </      className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </div>

                    <div>
                      <label htmlFor="taxNumber" className="block mb-2 text-sm font-medium">
                        <span>{t('settings.companyTaxNumber')}</span>
                        {lanhandleInvgiceSettiugsage === 'ar' ? (
                          <span className="text-xs text-gray-500 mr-1">Tax Number</span>
                        ) : (
                          <span className="text-xs text-gray-500 ml-1">الرقم الضريبي</span>
                  )}
                      </label>
                      <input
                        {t('settings.termsAndConditions')}
                  {language === 'ar' ? (
                          name="taxNumber"r
                  ) : (
                     span className="text-xs text-gray-500 ml-1">الشروط والأحكام< span>
                  )}
                </      type="text"
                        value={settings.companyInfo.taxNumber}
                        onChange={handleCompanyInfoChange}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </div>handleInviceSettigs
                  </div>
                </div>
              )}

        {activeTab === 'invoice' && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">
                    <spa{t('settings.invoiceFooter')}.invoic
                  {language === 'ar' ? (eSettings')}</span>
                      {language === 'ar' ? (r
                  ) : (
                     span className="text-xs text-gray-500 ml-1">تذييل الفاتورة</span>
                  )}
                <     <span className="text-xs text-gray-500 mr-1">Invoice Settings</span>
                    ) : (
                      <span className="text-xs text-gray-500 ml-1">إعدادات الفاتورة</span>
                    )}
                  </h2>
                  <div classhandleInvNiceSettiagsme="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="prefix" className="block mb-2 text-sm font-medium">
                        <sp>
              </divan>{t('settings.invoicePrefix')}</span>
                      {language === 'ar' ? (
            div>
        )}

        <div className="flex justify-end mt-6">
          <button
            type="submit"
              sabled={sa ing}
            className="px-4 py-2 bg-primary text-white rounded-md"
          >
            {saving ? t('settings.saving') : t('settings.saveSettings')}
          </button>
        </div>
      </form         <span className="text-xs text-gray-500 mr-1">Invoice Number Prefix</span>
                  ) : (
                    <span className="text-xs text-gray-500 ml-1">بادئة رقم الفاتورة</span>
                  )}
                </label>
                <input
                  id="prefix"
                  name="prefix"
                  type="text"
                  value={settings.invoiceSettings.prefix}
                  onChange={handleInvoiceSettingsChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label htmlFor="nextNumber" className="block mb-2 text-sm font-medium">
                  <span>{t('settings.nextInvoiceNumber')}</span>
                  {language === 'ar' ? (
                    <span className="text-xs text-gray-500 mr-1">Next Invoice Number</span>
                  ) : (
                    <span className="text-xs text-gray-500 ml-1">الرقم التالي للفاتورة</span>
                  )}
                </label>
                <input
                  id="nextNumber"
                  name="nextNumber"
                  type="number"
                  min="1"
                  value={settings.invoiceSettings.nextNumber}
                  onChange={handleInvoiceSettingsChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="termsAndConditions" className="block mb-2 text-sm font-medium">
                  <span>{t('settings.termsAndConditions')}</span>
                  {language === 'ar' ? (
                    <span className="text-xs text-gray-500 mr-1">Terms and Conditions</span>
                  ) : (
                    <span className="text-xs text-gray-500 ml-1">الشروط والأحكام</span>
                  )}
                </label>
                <textarea
                  id="termsAndConditions"
                  name="termsAndConditions"
                  value={settings.invoiceSettings.termsAndConditions}
                  onChange={handleInvoiceSettingsChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  rows={3}
                ></textarea>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="footer" className="block mb-2 text-sm font-medium">
                  <span>{t('settings.invoiceFooter')}</span>
                  {language === 'ar' ? (
                    <span className="text-xs text-gray-500 mr-1">Invoice Footer</span>
                  ) : (
                    <span className="text-xs text-gray-500 ml-1">تذييل الفاتورة</span>
                  )}
                </label>
                <textarea
                  id="footer"
                  name="footer"
                  value={settings.invoiceSettings.footer}
                  onChange={handleInvoiceSettingsChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  rows={2}
                ></textarea>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 bg-primary text-white rounded-md"
          >
            {saving ? t('settings.saving') : t('settings.saveSettings')}
          </button>
        </div>
      </form>
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma, calculatePagination, createPaginatedResult, createSearchFilter, generateInvoiceNumber } from '@/lib/prisma';

// GET: جلب الفواتير
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const customerId = searchParams.get('customerId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const { skip } = calculatePagination({ page, limit });

    // بناء شروط البحث
    const where: any = {};

    // إضافة البحث النصي
    if (search) {
      const searchFilter = createSearchFilter(search, ['number', 'notes']);
      Object.assign(where, searchFilter);
    }

    // إضافة فلتر العميل
    if (customerId) {
      where.customerId = parseInt(customerId);
    }

    // إضافة فلتر الحالة
    if (status) {
      where.status = status;
    }

    // جلب الفواتير مع العد الإجمالي
    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          customer: true,
          items: {
            include: {
              product: true
            }
          }
        }
      }),
      prisma.invoice.count({ where }),
    ]);

    // تحويل البيانات للتوافق مع الواجهة الأمامية
    const formattedInvoices = invoices.map(invoice => ({
      id: invoice.id.toString(),
      number: invoice.number,
      customer: {
        id: invoice.customer.id.toString(),
        name: invoice.customer.name,
        nameEn: invoice.customer.nameEn || '',
        email: invoice.customer.email || '',
        phone: invoice.customer.phone || '',
      },
      issueDate: invoice.issueDate,
      dueDate: invoice.dueDate,
      status: invoice.status,
      subtotal: invoice.subtotal,
      taxAmount: invoice.taxAmount,
      discountAmount: invoice.discountAmount,
      total: invoice.total,
      currency: invoice.currency,
      notes: invoice.notes || '',
      terms: invoice.terms || '',
      paymentStatus: invoice.paymentStatus,
      paidAt: invoice.paidAt?.toISOString() || null,
      items: invoice.items.map(item => ({
        id: item.id.toString(),
        productId: item.productId?.toString() || null,
        name: item.name,
        nameEn: item.nameEn || '',
        description: item.description || '',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        taxRate: item.taxRate,
        taxAmount: item.taxAmount,
        total: item.total,
        notes: item.notes || '',
      })),
      createdAt: invoice.createdAt.toISOString(),
      updatedAt: invoice.updatedAt.toISOString(),
    }));

    const paginatedResult = createPaginatedResult(formattedInvoices, total, page, limit);

    return NextResponse.json({
      invoices: paginatedResult.data,
      pagination: paginatedResult.pagination
    });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST: إنشاء فاتورة جديدة
export const POST = requireAuth(async (req: NextRequest, user: SessionUser) => {
  try {
    const requestData: {
      customerId: string;
      items: Array<{
        productId: string;
        description?: string;
        quantity: number;
        unitPrice: number;
        taxRate?: number;
        discount?: number;
      }>;
      taxRate?: number;
      discount?: number;
      dueDate?: string;
      notes?: string;
      status?: string;
    } = await req.json();

    // التحقق من البيانات المطلوبة
    if (!requestData.customerId || !requestData.items || requestData.items.length === 0) {
      return NextResponse.json(
        { error: 'البيانات المقدمة غير مكتملة' },
        { status: 400 }
      );
    }

    // حساب المجاميع
    const items = requestData.items.map((item) => {
      const itemTotal = item.quantity * item.unitPrice;
      const taxRate = item.taxRate || 15;
      const taxAmount = itemTotal * (taxRate / 100);
      const discount = item.discount || 0;
      const total = itemTotal + taxAmount - (itemTotal * discount / 100);

      return {
        productId: item.productId,
        description: item.description || '',
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        taxRate,
        taxAmount,
        discount,
        total
      };
    });

    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const taxRate = requestData.taxRate || 15;
    const taxAmount = subtotal * (taxRate / 100);
    const discount = requestData.discount || 0;
    const total = subtotal + taxAmount - (subtotal * discount / 100);

    // إنشاء رقم فاتورة فريد
    const invoiceCount = await prisma.invoice.count();
    const invoiceNumber = `INV-${new Date().getFullYear()}-${String(invoiceCount + 1).padStart(5, '0')}`;

    // إنشاء الفاتورة
    const newInvoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        customerId: requestData.customerId,
        userId: user.id,
        status: requestData.status || 'DRAFT',
        issueDate: new Date(),
        dueDate: requestData.dueDate ? new Date(requestData.dueDate) : undefined,
        subtotal,
        taxRate,
        taxAmount,
        discount,
        total,
        notes: requestData.notes || '',
        items: {
          create: items
        }
      },
      include: {
        customer: true,
        items: {
          include: {
            product: true
          }
        }
      }
    });

    return NextResponse.json(newInvoice, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء الفاتورة:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء الفاتورة' },
      { status: 500 }
    );
  }
});

// DELETE: حذف فاتورة
export const DELETE = requireAuth(async (req: NextRequest, user: SessionUser) => {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفاتورة وملكيتها
    const invoice = await prisma.invoice.findUnique({
      where: { id }
    });

    if (!invoice) {
      return NextResponse.json(
        { error: 'الفاتورة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من الصلاحيات (فقط المالك أو المدير يمكنه الحذف)
    if (invoice.userId !== user.id && user.role !== 'admin') {
      return NextResponse.json(
        { error: 'غير مصرح بحذف هذه الفاتورة' },
        { status: 403 }
      );
    }

    // حذف الفاتورة وعناصرها
    await prisma.invoice.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف الفاتورة:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف الفاتورة' },
      { status: 500 }
    );
  }
});

import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'

// تغيير حالة الفاتورة
export async function PATCH(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getServerSession()
        if (!session?.user) {
            return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
        }

        const data = await request.json()
        const { status } = data

        if (!status || !['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED'].includes(status)) {
            return NextResponse.json({ error: 'حالة الفاتورة غير صالحة' }, { status: 400 })
        }

        const updatedInvoice = await prisma.invoice.update({
            where: { id: params.id },
            data: { status },
            include: {
                customer: true,
                items: true,
            },
        })

        return NextResponse.json(updatedInvoice)
    } catch (error) {
        console.error('خطأ في تغيير حالة الفاتورة:', error)
        return NextResponse.json({ error: 'حدث خطأ أثناء معالجة الطلب' }, { status: 500 })
    }
}
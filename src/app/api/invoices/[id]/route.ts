import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

// بيانات تجريبية للفواتير (نفس البيانات من route.ts الرئيسي)
let invoices = [
  {
    id: '1',
    number: 'INV-2024-001',
    issueDate: '2024-01-15',
    dueDate: '2024-02-15',
    status: 'paid',
    customerId: '1',
    customer: {
      id: '1',
      name: 'شركة الإمارات للتجارة',
      nameEn: 'Emirates Trading Company',
      email: '<EMAIL>',
      phone: '+971 4 123 4567',
      address: 'شارع الشيخ زايد، دبي، الإمارات العربية المتحدة',
      taxNumber: '100123456789003'
    },
    items: [
      {
        id: '1',
        productId: '1',
        name: 'خدمات استشارية تقنية',
        nameEn: 'Technical Consulting Services',
        description: 'استشارات تقنية متخصصة لمدة 40 ساعة',
        quantity: 40,
        unitPrice: 500,
        taxRate: 5,
        taxAmount: 1000,
        total: 21000
      }
    ],
    subtotal: 20000,
    taxAmount: 1000,
    discountAmount: 0,
    total: 21000,
    currency: 'AED',
    notes: 'شكراً لتعاملكم معنا',
    terms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    paidAt: '2024-01-20T14:30:00Z'
  },
  {
    id: '2',
    number: 'INV-2024-002',
    issueDate: '2024-01-20',
    dueDate: '2024-02-20',
    status: 'pending',
    customerId: '2',
    customer: {
      id: '2',
      name: 'مؤسسة الخليج للمقاولات',
      nameEn: 'Gulf Contracting Est.',
      email: '<EMAIL>',
      phone: '+971 2 987 6543',
      address: 'شارع الكورنيش، أبوظبي، الإمارات العربية المتحدة',
      taxNumber: '100987654321003'
    },
    items: [
      {
        id: '1',
        productId: '2',
        name: 'تطوير تطبيقات الويب',
        nameEn: 'Web Application Development',
        description: 'تطوير موقع إلكتروني متكامل',
        quantity: 1,
        unitPrice: 2500,
        taxRate: 5,
        taxAmount: 125,
        total: 2625
      },
      {
        id: '2',
        productId: '4',
        name: 'أجهزة كمبيوتر محمولة',
        nameEn: 'Laptop Computers',
        description: 'أجهزة كمبيوتر محمولة للفريق',
        quantity: 3,
        unitPrice: 3000,
        taxRate: 5,
        taxAmount: 450,
        total: 9450
      }
    ],
    subtotal: 11500,
    taxAmount: 575,
    discountAmount: 0,
    total: 12075,
    currency: 'AED',
    notes: 'يرجى الدفع في الموعد المحدد',
    terms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    createdAt: '2024-01-20T09:00:00Z',
    updatedAt: '2024-01-20T09:00:00Z'
  },
  {
    id: '3',
    number: 'INV-2024-003',
    issueDate: '2024-01-25',
    dueDate: '2024-02-25',
    status: 'draft',
    customerId: '3',
    customer: {
      id: '3',
      name: 'شركة النور للتكنولوجيا',
      nameEn: 'Al Noor Technology LLC',
      email: '<EMAIL>',
      phone: '+971 6 555 1234',
      address: 'المدينة الصناعية، الشارقة، الإمارات العربية المتحدة',
      taxNumber: '100555123456003'
    },
    items: [
      {
        id: '1',
        productId: '5',
        name: 'برامج إدارة المشاريع',
        nameEn: 'Project Management Software',
        description: 'تراخيص برامج إدارة المشاريع',
        quantity: 10,
        unitPrice: 1200,
        taxRate: 5,
        taxAmount: 600,
        total: 12600
      }
    ],
    subtotal: 12000,
    taxAmount: 600,
    discountAmount: 0,
    total: 12600,
    currency: 'AED',
    notes: 'مسودة فاتورة - لم يتم الإرسال بعد',
    terms: 'الدفع خلال 30 يوم من تاريخ الفاتورة',
    createdAt: '2024-01-25T11:00:00Z',
    updatedAt: '2024-01-25T11:00:00Z'
  }
];

// GET - جلب فاتورة محددة
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const invoice = invoices.find(inv => inv.id === params.id);
    
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    return NextResponse.json(invoice);
  } catch (error) {
    console.error('Error fetching invoice:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT - تحديث فاتورة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const invoiceIndex = invoices.findIndex(inv => inv.id === params.id);
    
    if (invoiceIndex === -1) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // التحقق من صحة البيانات
    if (!body.customerId || !body.items || body.items.length === 0) {
      return NextResponse.json({ 
        error: 'Customer and at least one item are required' 
      }, { status: 400 });
    }

    // حساب المجاميع
    let subtotal = 0;
    let taxAmount = 0;
    
    const processedItems = body.items.map((item: any, index: number) => {
      const itemSubtotal = item.quantity * item.unitPrice;
      const itemTaxAmount = (itemSubtotal * (item.taxRate || 0)) / 100;
      const itemTotal = itemSubtotal + itemTaxAmount;
      
      subtotal += itemSubtotal;
      taxAmount += itemTaxAmount;
      
      return {
        ...item,
        taxAmount: itemTaxAmount,
        total: itemTotal
      };
    });

    const discountAmount = body.discountAmount || 0;
    const total = subtotal + taxAmount - discountAmount;

    const updatedInvoice = {
      ...invoices[invoiceIndex],
      customerId: body.customerId,
      customer: body.customer,
      issueDate: body.issueDate || invoices[invoiceIndex].issueDate,
      dueDate: body.dueDate || invoices[invoiceIndex].dueDate,
      status: body.status || invoices[invoiceIndex].status,
      items: processedItems,
      subtotal,
      taxAmount,
      discountAmount,
      total,
      currency: body.currency || 'AED',
      notes: body.notes || '',
      terms: body.terms || 'الدفع خلال 30 يوم من تاريخ الفاتورة',
      updatedAt: new Date().toISOString()
    };

    invoices[invoiceIndex] = updatedInvoice;

    return NextResponse.json(updatedInvoice);
  } catch (error) {
    console.error('Error updating invoice:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE - حذف فاتورة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const invoiceIndex = invoices.findIndex(inv => inv.id === params.id);
    
    if (invoiceIndex === -1) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    const deletedInvoice = invoices[invoiceIndex];
    invoices.splice(invoiceIndex, 1);

    return NextResponse.json({ 
      message: 'Invoice deleted successfully',
      invoice: deletedInvoice 
    });
  } catch (error) {
    console.error('Error deleting invoice:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

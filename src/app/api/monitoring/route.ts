import { NextRequest, NextResponse } from 'next/server';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';
import { applicationMonitor } from '@/lib/monitoring';
import { logger } from '@/lib/logger';

/**
 * الحصول على مقاييس المراقبة
 */
export const GET = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const { searchParams } = new URL(request.url);
      const timeWindow = parseInt(searchParams.get('timeWindow') || '300000'); // 5 minutes default
      const format = searchParams.get('format') || 'json';

      if (format === 'prometheus') {
        // تصدير بتنسيق Prometheus
        const prometheusMetrics = applicationMonitor.exportPrometheusMetrics();
        
        return new NextResponse(prometheusMetrics, {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8'
          }
        });
      }

      // الحصول على جميع المقاييس
      const metrics = applicationMonitor.getAllMetrics(timeWindow);
      
      // الحصول على تقرير صحة النظام
      const healthReport = applicationMonitor.getHealthReport();
      
      // الحصول على إحصائيات السجلات
      const logStats = await logger.getLogStats();

      return NextResponse.json({
        success: true,
        data: {
          metrics,
          health: healthReport,
          logs: logStats,
          timeWindow,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Failed to get monitoring data', error, {
        userId: user.id,
        endpoint: '/api/monitoring'
      });

      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get monitoring data'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL
  }
);

/**
 * إدارة التنبيهات والمراقبة
 */
export const POST = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json();
      const { action, alertRule, metricName } = body;

      switch (action) {
        case 'add_alert':
          if (!alertRule) {
            return NextResponse.json(
              { success: false, error: 'Alert rule is required' },
              { status: 400 }
            );
          }

          applicationMonitor.addAlert(alertRule);
          
          logger.info('Alert rule added', {
            userId: user.id,
            alertRule: alertRule.name,
            metric: alertRule.metric
          });

          return NextResponse.json({
            success: true,
            message: 'Alert rule added successfully'
          });

        case 'get_metric_stats':
          if (!metricName) {
            return NextResponse.json(
              { success: false, error: 'Metric name is required' },
              { status: 400 }
            );
          }

          const timeWindow = body.timeWindow || 3600000; // 1 hour default
          const stats = applicationMonitor.getMetricStats(metricName, timeWindow);

          return NextResponse.json({
            success: true,
            data: {
              metric: metricName,
              stats,
              timeWindow
            }
          });

        case 'record_custom_metric':
          const { name, value, tags, unit } = body;
          
          if (!name || value === undefined) {
            return NextResponse.json(
              { success: false, error: 'Metric name and value are required' },
              { status: 400 }
            );
          }

          applicationMonitor.recordMetric(name, value, tags, unit);
          
          logger.debug('Custom metric recorded', {
            userId: user.id,
            metricName: name,
            value,
            tags
          });

          return NextResponse.json({
            success: true,
            message: 'Metric recorded successfully'
          });

        case 'get_health_report':
          const healthReport = applicationMonitor.getHealthReport();
          
          return NextResponse.json({
            success: true,
            data: healthReport
          });

        case 'clear_metrics':
          // مسح مقاييس محددة (للاختبار فقط)
          if (process.env.NODE_ENV === 'development') {
            // هذه العملية خطيرة ويجب أن تكون محدودة
            logger.warn('Metrics cleared by admin', {
              userId: user.id,
              userName: user.name
            });

            return NextResponse.json({
              success: true,
              message: 'Metrics cleared (development only)'
            });
          } else {
            return NextResponse.json(
              { success: false, error: 'Operation not allowed in production' },
              { status: 403 }
            );
          }

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          );
      }

    } catch (error) {
      logger.error('Failed to process monitoring request', error, {
        userId: user.id,
        endpoint: '/api/monitoring'
      });

      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to process request'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.SENSITIVE_OPS,
    validateInput: true
  }
);

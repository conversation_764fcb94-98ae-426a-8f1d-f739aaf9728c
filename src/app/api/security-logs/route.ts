import { NextRequest, NextResponse } from 'next/server';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';
import { getUserSecurityLogs, getSecurityLogsByType } from '@/lib/security-logger';
import { prisma } from '@/lib/prisma';

/**
 * الحصول على سجلات الأمان
 */
export const GET = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const { searchParams } = new URL(request.url);
      const userId = searchParams.get('userId');
      const type = searchParams.get('type');
      const severity = searchParams.get('severity');
      const limit = parseInt(searchParams.get('limit') || '50');
      const offset = parseInt(searchParams.get('offset') || '0');
      const startDate = searchParams.get('startDate');
      const endDate = searchParams.get('endDate');

      let logs;
      let total;

      if (userId && user.role === 'admin') {
        // سجلات مستخدم محدد (للمشرفين فقط)
        logs = await getUserSecurityLogs(userId, limit);
        total = logs.length;
      } else if (type) {
        // سجلات حسب النوع
        logs = await getSecurityLogsByType(type as any, limit);
        total = logs.length;
      } else {
        // سجلات عامة مع فلترة
        const where: any = {};
        
        // فلترة حسب المستخدم (المستخدم العادي يرى سجلاته فقط)
        if (user.role !== 'admin') {
          where.userId = user.id;
        }
        
        // فلترة حسب الخطورة
        if (severity) {
          where.severity = severity;
        }
        
        // فلترة حسب التاريخ
        if (startDate || endDate) {
          where.timestamp = {};
          if (startDate) {
            where.timestamp.gte = new Date(startDate);
          }
          if (endDate) {
            where.timestamp.lte = new Date(endDate);
          }
        }

        // الحصول على العدد الإجمالي
        total = await prisma.securityLog.count({ where });

        // الحصول على السجلات
        logs = await prisma.securityLog.findMany({
          where,
          orderBy: { timestamp: 'desc' },
          take: limit,
          skip: offset,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        });
      }

      return NextResponse.json({
        success: true,
        logs,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      });

    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to fetch security logs'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredPermissions: ['read:security_logs'],
    rateLimit: RATE_LIMITS.API_GENERAL
  }
);

/**
 * الحصول على إحصائيات الأمان
 */
export const POST = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json().catch(() => ({}));
      const days = body.days || 7;
      
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // إحصائيات عامة
      const totalLogs = await prisma.securityLog.count({
        where: {
          timestamp: { gte: startDate }
        }
      });

      // إحصائيات حسب النوع
      const logsByType = await prisma.securityLog.groupBy({
        by: ['type'],
        where: {
          timestamp: { gte: startDate }
        },
        _count: {
          type: true
        },
        orderBy: {
          _count: {
            type: 'desc'
          }
        }
      });

      // إحصائيات حسب الخطورة
      const logsBySeverity = await prisma.securityLog.groupBy({
        by: ['severity'],
        where: {
          timestamp: { gte: startDate }
        },
        _count: {
          severity: true
        }
      });

      // أكثر عناوين IP نشاطاً
      const topIPs = await prisma.securityLog.groupBy({
        by: ['ip'],
        where: {
          timestamp: { gte: startDate },
          ip: { not: null }
        },
        _count: {
          ip: true
        },
        orderBy: {
          _count: {
            ip: 'desc'
          }
        },
        take: 10
      });

      // الأحداث الحرجة الأخيرة
      const criticalEvents = await prisma.securityLog.findMany({
        where: {
          severity: 'CRITICAL',
          timestamp: { gte: startDate }
        },
        orderBy: { timestamp: 'desc' },
        take: 10,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      // إحصائيات يومية
      const dailyStats = await prisma.securityLog.groupBy({
        by: ['timestamp'],
        where: {
          timestamp: { gte: startDate }
        },
        _count: {
          id: true
        }
      });

      // تجميع الإحصائيات اليومية
      const dailyStatsFormatted = dailyStats.reduce((acc: any, stat) => {
        const date = stat.timestamp.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + stat._count.id;
        return acc;
      }, {});

      return NextResponse.json({
        success: true,
        period: {
          days,
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString()
        },
        summary: {
          totalLogs,
          criticalEvents: criticalEvents.length,
          uniqueIPs: topIPs.length
        },
        breakdown: {
          byType: logsByType.map(item => ({
            type: item.type,
            count: item._count.type
          })),
          bySeverity: logsBySeverity.map(item => ({
            severity: item.severity,
            count: item._count.severity
          })),
          topIPs: topIPs.map(item => ({
            ip: item.ip,
            count: item._count.ip
          }))
        },
        criticalEvents,
        dailyStats: dailyStatsFormatted
      });

    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to generate security statistics'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL,
    validateInput: true
  }
);

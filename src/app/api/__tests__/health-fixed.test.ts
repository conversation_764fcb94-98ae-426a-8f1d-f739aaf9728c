import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { GET, POST } from '../health/route';

// Mock dependencies
jest.mock('@/lib/health-monitor');
jest.mock('@/lib/api-auth');

// Mock NextRequest
class MockNextRequest {
  constructor(public url: string, public init?: RequestInit) {}
  get method() { return this.init?.method || 'GET'; }
  get headers() { return new Map(Object.entries(this.init?.headers || {})); }
}

const mockPerformHealthCheck = jest.fn();
const mockPerformanceMonitor = {
  getAllMetrics: jest.fn()
};

// Mock the health monitor
jest.mocked(require('@/lib/health-monitor')).performHealthCheck = mockPerformHealthCheck;
jest.mocked(require('@/lib/health-monitor')).performanceMonitor = mockPerformanceMonitor;

// Mock the API auth
const mockProtectApiRoute = jest.fn();
jest.mocked(require('@/lib/api-auth')).protectApiRoute = mockProtectApiRoute;

describe('Health API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    mockPerformHealthCheck.mockResolvedValue({
      overall: 'healthy',
      checks: [
        {
          service: 'database',
          status: 'healthy',
          responseTime: 50,
          details: 'Connected successfully',
          timestamp: new Date()
        }
      ],
      uptime: 3600,
      memory: {
        used: 50 * 1024 * 1024,
        total: 100 * 1024 * 1024,
        percentage: 50
      }
    });

    mockPerformanceMonitor.getAllMetrics.mockReturnValue({
      requests: 100,
      errors: 5,
      averageResponseTime: 200
    });

    mockProtectApiRoute.mockResolvedValue(null);
  });

  describe('GET /api/health', () => {
    it('should return health status', async () => {
      const request = new MockNextRequest('http://localhost:3000/api/health') as any;
      const response = await GET(request);
      
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.overall).toBe('healthy');
    });

    it('should handle health check errors', async () => {
      mockPerformHealthCheck.mockRejectedValue(new Error('Health check failed'));
      
      const request = new MockNextRequest('http://localhost:3000/api/health') as any;
      const response = await GET(request);
      
      expect(response.status).toBe(500);
    });
  });

  describe('POST /api/health', () => {
    it('should return performance metrics', async () => {
      const request = new MockNextRequest('http://localhost:3000/api/health', {
        method: 'POST'
      }) as any;
      
      const response = await POST(request);
      
      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.requests).toBe(100);
    });

    it('should handle authentication errors', async () => {
      mockProtectApiRoute.mockResolvedValue(new Response('Unauthorized', { status: 401 }));
      
      const request = new MockNextRequest('http://localhost:3000/api/health', {
        method: 'POST'
      }) as any;
      
      const response = await POST(request);
      
      expect(response.status).toBe(401);
    });
  });
});

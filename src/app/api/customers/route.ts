import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

// بيانات تجريبية للعملاء
let customers = [
  {
    id: '1',
    name: 'شركة الإمارات للتجارة',
    nameEn: 'Emirates Trading Company',
    email: '<EMAIL>',
    phone: '+971 4 123 4567',
    address: 'شارع الشيخ زايد، دبي، الإمارات العربية المتحدة',
    city: 'دبي',
    country: 'الإمارات العربية المتحدة',
    taxNumber: '100123456789003',
    contactPerson: 'أحمد محمد',
    contactPhone: '+971 50 123 4567',
    contactEmail: '<EMAIL>',
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'مؤسسة الخليج للمقاولات',
    nameEn: 'Gulf Contracting Est.',
    email: '<EMAIL>',
    phone: '+971 2 987 6543',
    address: 'شارع الكورنيش، أبوظبي، الإمارات العربية المتحدة',
    city: 'أبوظبي',
    country: 'الإمارات العربية المتحدة',
    taxNumber: '100987654321003',
    contactPerson: 'فاطمة علي',
    contactPhone: '+971 55 987 6543',
    contactEmail: '<EMAIL>',
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '3',
    name: 'شركة النور للتكنولوجيا',
    nameEn: 'Al Noor Technology LLC',
    email: '<EMAIL>',
    phone: '+971 6 555 1234',
    address: 'المدينة الصناعية، الشارقة، الإمارات العربية المتحدة',
    city: 'الشارقة',
    country: 'الإمارات العربية المتحدة',
    taxNumber: '100555123456003',
    contactPerson: 'محمد خالد',
    contactPhone: '+971 52 555 1234',
    contactEmail: '<EMAIL>',
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// GET - جلب جميع العملاء
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let filteredCustomers = [...customers];

    // تطبيق البحث
    if (search) {
      filteredCustomers = filteredCustomers.filter(customer =>
        customer.name.toLowerCase().includes(search.toLowerCase()) ||
        customer.nameEn.toLowerCase().includes(search.toLowerCase()) ||
        customer.email.toLowerCase().includes(search.toLowerCase()) ||
        customer.phone.includes(search)
      );
    }

    // تطبيق فلتر الحالة
    if (status) {
      filteredCustomers = filteredCustomers.filter(customer => customer.status === status);
    }

    // تطبيق التصفح
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);

    return NextResponse.json({
      customers: paginatedCustomers,
      pagination: {
        page,
        limit,
        total: filteredCustomers.length,
        totalPages: Math.ceil(filteredCustomers.length / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST - إنشاء عميل جديد
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // التحقق من صحة البيانات
    if (!body.name || !body.email) {
      return NextResponse.json({ error: 'Name and email are required' }, { status: 400 });
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    const existingCustomer = customers.find(customer => customer.email === body.email);
    if (existingCustomer) {
      return NextResponse.json({ error: 'Customer with this email already exists' }, { status: 409 });
    }

    const newCustomer = {
      id: (customers.length + 1).toString(),
      name: body.name,
      nameEn: body.nameEn || '',
      email: body.email,
      phone: body.phone || '',
      address: body.address || '',
      city: body.city || '',
      country: body.country || 'الإمارات العربية المتحدة',
      taxNumber: body.taxNumber || '',
      contactPerson: body.contactPerson || '',
      contactPhone: body.contactPhone || '',
      contactEmail: body.contactEmail || '',
      status: body.status || 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    customers.push(newCustomer);

    return NextResponse.json(newCustomer, { status: 201 });
  } catch (error) {
    console.error('Error creating customer:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { performHealthCheck, performanceMonitor } from '@/lib/health-monitor';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';

/**
 * فحص صحة النظام العام - متاح للجميع
 */
export async function GET(request: NextRequest) {
  try {
    const healthStatus = await performHealthCheck();
    
    // إضافة معلومات الأداء
    const performanceMetrics = performanceMonitor.getAllMetrics();
    
    const response = {
      ...healthStatus,
      performance: performanceMetrics,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString()
    };
    
    // تحديد status code بناءً على حالة النظام
    const statusCode = healthStatus.overall === 'healthy' ? 200 : 
                      healthStatus.overall === 'degraded' ? 200 : 503;
    
    return NextResponse.json(response, { status: statusCode });
    
  } catch (error) {
    return NextResponse.json(
      {
        overall: 'unhealthy',
        error: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString()
      },
      { status: 503 }
    );
  }
}

/**
 * فحص صحة النظام المفصل - يتطلب صلاحيات إدارية
 */
export const POST = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json().catch(() => ({}));
      const includeDetails = body.includeDetails !== false;
      
      const healthStatus = await performHealthCheck();
      
      if (!includeDetails) {
        // إرجاع معلومات مبسطة
        return NextResponse.json({
          overall: healthStatus.overall,
          uptime: healthStatus.uptime,
          timestamp: new Date().toISOString()
        });
      }
      
      // إرجاع معلومات مفصلة
      const performanceMetrics = performanceMonitor.getAllMetrics();
      
      const detailedResponse = {
        ...healthStatus,
        performance: performanceMetrics,
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          pid: process.pid,
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          cpuUsage: process.cpuUsage()
        },
        environment: {
          nodeEnv: process.env.NODE_ENV,
          version: process.env.npm_package_version || '1.0.0'
        },
        requestedBy: {
          userId: user.id,
          userName: user.name,
          role: user.role
        },
        timestamp: new Date().toISOString()
      };
      
      return NextResponse.json(detailedResponse);
      
    } catch (error) {
      return NextResponse.json(
        {
          error: error instanceof Error ? error.message : 'Detailed health check failed',
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL,
    validateInput: false
  }
);

import { NextRequest, NextResponse } from 'next/server';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';
import { analyzeDatabasePerformance, cleanupOldData, queryMonitor } from '@/lib/database-optimizer';

/**
 * تحليل أداء قاعدة البيانات
 */
export const GET = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const analysis = await analyzeDatabasePerformance();

      return NextResponse.json({
        success: true,
        analysis,
        timestamp: new Date().toISOString(),
        analyzedBy: user.name
      });

    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Database analysis failed'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL
  }
);

/**
 * تنفيذ عمليات تحسين قاعدة البيانات
 */
export const POST = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json();
      const { action, options = {} } = body;

      switch (action) {
        case 'cleanup_old_data':
          const cleanupResult = await cleanupOldData({
            securityLogsDays: options.securityLogsDays || 90,
            expensesDays: options.expensesDays || 365,
            dryRun: options.dryRun || false
          });

          return NextResponse.json({
            success: cleanupResult.success,
            result: cleanupResult,
            message: cleanupResult.success 
              ? `تم تنظيف ${Object.values(cleanupResult.cleaned).reduce((sum, count) => sum + count, 0)} سجل`
              : 'فشل في تنظيف البيانات',
            executedBy: user.name,
            timestamp: new Date().toISOString()
          });

        case 'clear_slow_queries':
          queryMonitor.clearSlowQueries();
          
          return NextResponse.json({
            success: true,
            message: 'تم مسح سجلات الاستعلامات البطيئة',
            executedBy: user.name,
            timestamp: new Date().toISOString()
          });

        case 'get_slow_queries':
          const slowQueries = queryMonitor.getSlowQueries(options.limit || 20);
          const performanceStats = queryMonitor.getPerformanceStats();
          
          return NextResponse.json({
            success: true,
            slowQueries,
            performanceStats,
            timestamp: new Date().toISOString()
          });

        case 'analyze_performance':
          const analysis = await analyzeDatabasePerformance();
          
          return NextResponse.json({
            success: true,
            analysis,
            timestamp: new Date().toISOString(),
            analyzedBy: user.name
          });

        default:
          return NextResponse.json(
            { success: false, error: 'إجراء غير صالح' },
            { status: 400 }
          );
      }

    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Database operation failed'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.SENSITIVE_OPS,
    validateInput: true
  }
);

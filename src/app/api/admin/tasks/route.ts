import { NextRequest, NextResponse } from 'next/server';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';
import { taskScheduler, runTaskNow, getTaskStatistics } from '@/lib/scheduled-tasks';

/**
 * الحصول على حالة المهام المجدولة
 */
export const GET = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const tasks = taskScheduler.getTasksStatus();
      const statistics = getTaskStatistics();

      return NextResponse.json({
        success: true,
        tasks,
        statistics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get tasks status'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL
  }
);

/**
 * تشغيل مهمة فورية أو تعديل إعدادات المهام
 */
export const POST = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json();
      const { action, taskName } = body;

      switch (action) {
        case 'run_now':
          if (!taskName) {
            return NextResponse.json(
              { success: false, error: 'اسم المهمة مطلوب' },
              { status: 400 }
            );
          }

          const result = await runTaskNow(taskName);
          
          if (result.success) {
            return NextResponse.json({
              success: true,
              message: `تم تشغيل المهمة ${taskName} بنجاح`,
              executedBy: user.name,
              timestamp: new Date().toISOString()
            });
          } else {
            return NextResponse.json(
              {
                success: false,
                error: result.error || 'فشل في تشغيل المهمة'
              },
              { status: 500 }
            );
          }

        case 'enable':
          if (!taskName) {
            return NextResponse.json(
              { success: false, error: 'اسم المهمة مطلوب' },
              { status: 400 }
            );
          }

          taskScheduler.enableTask(taskName);
          
          return NextResponse.json({
            success: true,
            message: `تم تفعيل المهمة ${taskName}`,
            modifiedBy: user.name,
            timestamp: new Date().toISOString()
          });

        case 'disable':
          if (!taskName) {
            return NextResponse.json(
              { success: false, error: 'اسم المهمة مطلوب' },
              { status: 400 }
            );
          }

          taskScheduler.disableTask(taskName);
          
          return NextResponse.json({
            success: true,
            message: `تم تعطيل المهمة ${taskName}`,
            modifiedBy: user.name,
            timestamp: new Date().toISOString()
          });

        default:
          return NextResponse.json(
            { success: false, error: 'إجراء غير صالح' },
            { status: 400 }
          );
      }

    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Task operation failed'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.SENSITIVE_OPS,
    validateInput: true
  }
);

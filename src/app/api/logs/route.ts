import { NextRequest, NextResponse } from 'next/server';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';
import { logger } from '@/lib/logger';
import fs from 'fs/promises';
import path from 'path';

/**
 * الحصول على السجلات
 */
export const GET = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const { searchParams } = new URL(request.url);
      const level = searchParams.get('level');
      const limit = parseInt(searchParams.get('limit') || '100');
      const offset = parseInt(searchParams.get('offset') || '0');
      const startDate = searchParams.get('startDate');
      const endDate = searchParams.get('endDate');
      const search = searchParams.get('search');
      const format = searchParams.get('format') || 'json';

      // الحصول على إحصائيات السجلات
      const logStats = await logger.getLogStats();

      if (format === 'stats') {
        return NextResponse.json({
          success: true,
          data: logStats
        });
      }

      // قراءة ملفات السجل
      const logs = await readLogFiles({
        level,
        limit,
        offset,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        search
      });

      if (format === 'text') {
        const textLogs = logs.map(log => 
          typeof log === 'string' ? log : JSON.stringify(log)
        ).join('\n');

        return new NextResponse(textLogs, {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
            'Content-Disposition': `attachment; filename="logs-${new Date().toISOString().split('T')[0]}.txt"`
          }
        });
      }

      return NextResponse.json({
        success: true,
        data: {
          logs,
          stats: logStats,
          pagination: {
            limit,
            offset,
            total: logs.length
          }
        }
      });

    } catch (error) {
      logger.error('Failed to get logs', error, {
        userId: user.id,
        endpoint: '/api/logs'
      });

      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get logs'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL
  }
);

/**
 * إدارة السجلات
 */
export const POST = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json();
      const { action, level, message, context } = body;

      switch (action) {
        case 'log_message':
          if (!level || !message) {
            return NextResponse.json(
              { success: false, error: 'Level and message are required' },
              { status: 400 }
            );
          }

          // إضافة معلومات المستخدم إلى السياق
          const logContext = {
            ...context,
            userId: user.id,
            userName: user.name,
            manualLog: true
          };

          // تسجيل الرسالة
          switch (level) {
            case 'debug':
              logger.debug(message, logContext);
              break;
            case 'info':
              logger.info(message, logContext);
              break;
            case 'warn':
              logger.warn(message, logContext);
              break;
            case 'error':
              logger.error(message, undefined, logContext);
              break;
            case 'fatal':
              logger.fatal(message, undefined, logContext);
              break;
            default:
              return NextResponse.json(
                { success: false, error: 'Invalid log level' },
                { status: 400 }
              );
          }

          return NextResponse.json({
            success: true,
            message: 'Log message recorded successfully'
          });

        case 'clear_logs':
          // مسح السجلات (للتطوير فقط)
          if (process.env.NODE_ENV === 'development') {
            await clearLogFiles();
            
            logger.warn('Log files cleared by admin', {
              userId: user.id,
              userName: user.name
            });

            return NextResponse.json({
              success: true,
              message: 'Log files cleared (development only)'
            });
          } else {
            return NextResponse.json(
              { success: false, error: 'Operation not allowed in production' },
              { status: 403 }
            );
          }

        case 'rotate_logs':
          // تدوير السجلات يدوياً
          logger.info('Manual log rotation initiated', {
            userId: user.id,
            userName: user.name
          });

          return NextResponse.json({
            success: true,
            message: 'Log rotation initiated'
          });

        case 'export_logs':
          const exportFormat = body.format || 'json';
          const exportStartDate = body.startDate ? new Date(body.startDate) : undefined;
          const exportEndDate = body.endDate ? new Date(body.endDate) : undefined;

          const exportLogs = await readLogFiles({
            startDate: exportStartDate,
            endDate: exportEndDate,
            limit: 10000 // حد أقصى للتصدير
          });

          logger.info('Logs exported', {
            userId: user.id,
            userName: user.name,
            format: exportFormat,
            count: exportLogs.length
          });

          return NextResponse.json({
            success: true,
            data: {
              logs: exportLogs,
              format: exportFormat,
              exportedAt: new Date().toISOString(),
              exportedBy: user.name
            }
          });

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          );
      }

    } catch (error) {
      logger.error('Failed to process logs request', error, {
        userId: user.id,
        endpoint: '/api/logs'
      });

      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to process request'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.SENSITIVE_OPS,
    validateInput: true
  }
);

/**
 * قراءة ملفات السجل
 */
async function readLogFiles(options: {
  level?: string | null;
  limit?: number;
  offset?: number;
  startDate?: Date;
  endDate?: Date;
  search?: string | null;
}): Promise<any[]> {
  const logFilePath = process.env.LOG_FILE_PATH || './logs/app.log';
  
  try {
    // قراءة الملف الرئيسي
    const content = await fs.readFile(logFilePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());
    
    let logs: any[] = [];
    
    for (const line of lines) {
      try {
        // محاولة تحليل JSON
        const logEntry = JSON.parse(line);
        logs.push(logEntry);
      } catch {
        // إذا لم يكن JSON، أضف كنص عادي
        logs.push({ message: line, timestamp: new Date().toISOString() });
      }
    }

    // تطبيق الفلاتر
    if (options.level) {
      logs = logs.filter(log => log.level === options.level);
    }

    if (options.startDate) {
      logs = logs.filter(log => new Date(log.timestamp) >= options.startDate!);
    }

    if (options.endDate) {
      logs = logs.filter(log => new Date(log.timestamp) <= options.endDate!);
    }

    if (options.search) {
      const searchTerm = options.search.toLowerCase();
      logs = logs.filter(log => 
        JSON.stringify(log).toLowerCase().includes(searchTerm)
      );
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // تطبيق التصفح
    const offset = options.offset || 0;
    const limit = options.limit || 100;
    
    return logs.slice(offset, offset + limit);

  } catch (error) {
    console.error('Error reading log files:', error);
    return [];
  }
}

/**
 * مسح ملفات السجل
 */
async function clearLogFiles(): Promise<void> {
  const logFilePath = process.env.LOG_FILE_PATH || './logs/app.log';
  
  try {
    await fs.writeFile(logFilePath, '', 'utf8');
  } catch (error) {
    console.error('Error clearing log files:', error);
    throw error;
  }
}

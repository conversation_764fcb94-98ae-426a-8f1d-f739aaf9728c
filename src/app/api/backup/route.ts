import { NextRequest, NextResponse } from 'next/server';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';
import { createDatabaseBackup, listBackups, restoreFromBackup } from '@/lib/backup-system';

/**
 * الحصول على قائمة النسخ الاحتياطية
 */
export const GET = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const backups = await listBackups();
      
      return NextResponse.json({
        success: true,
        backups: backups.map(backup => ({
          fileName: backup.fileName,
          size: backup.size,
          createdAt: backup.createdAt,
          isEncrypted: backup.isEncrypted,
          isCompressed: backup.isCompressed,
          sizeFormatted: formatFileSize(backup.size)
        })),
        total: backups.length
      });
      
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to list backups'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL
  }
);

/**
 * إنشاء نسخة احتياطية جديدة
 */
export const POST = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json().catch(() => ({}));
      
      const config = {
        compressionEnabled: body.compression !== false,
        encryptionEnabled: body.encryption !== false
      };
      
      const result = await createDatabaseBackup(config);
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
          backup: {
            filePath: result.filePath,
            size: result.size,
            sizeFormatted: result.size ? formatFileSize(result.size) : undefined,
            duration: result.duration,
            createdBy: user.name,
            createdAt: new Date().toISOString()
          }
        });
      } else {
        return NextResponse.json(
          {
            success: false,
            error: result.error || 'فشل في إنشاء النسخة الاحتياطية'
          },
          { status: 500 }
        );
      }
      
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Backup creation failed'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: { requests: 5, windowMs: 60 * 60 * 1000 }, // 5 نسخ في الساعة
    validateInput: true
  }
);

/**
 * استعادة من نسخة احتياطية
 */
export const PUT = protectApiRoute(
  async (request: NextRequest, user: any) => {
    try {
      const body = await request.json();
      
      if (!body.backupFileName) {
        return NextResponse.json(
          {
            success: false,
            error: 'اسم ملف النسخة الاحتياطية مطلوب'
          },
          { status: 400 }
        );
      }
      
      // التحقق من وجود النسخة الاحتياطية
      const backups = await listBackups();
      const targetBackup = backups.find(backup => backup.fileName === body.backupFileName);
      
      if (!targetBackup) {
        return NextResponse.json(
          {
            success: false,
            error: 'النسخة الاحتياطية المطلوبة غير موجودة'
          },
          { status: 404 }
        );
      }
      
      const result = await restoreFromBackup(targetBackup.filePath);
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          message: 'تم استعادة قاعدة البيانات بنجاح',
          restore: {
            backupFile: body.backupFileName,
            duration: result.duration,
            restoredBy: user.name,
            restoredAt: new Date().toISOString()
          }
        });
      } else {
        return NextResponse.json(
          {
            success: false,
            error: result.error || 'فشل في استعادة قاعدة البيانات'
          },
          { status: 500 }
        );
      }
      
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Restore failed'
        },
        { status: 500 }
      );
    }
  },
  {
    requiredRole: 'admin',
    rateLimit: { requests: 3, windowMs: 60 * 60 * 1000 }, // 3 استعادات في الساعة
    validateInput: true
  }
);

/**
 * تنسيق حجم الملف
 */
function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

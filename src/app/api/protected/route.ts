import { NextRequest, NextResponse } from 'next/server';
import { protectApiRoute } from '@/lib/api-auth';
import { RATE_LIMITS } from '@/lib/rate-limit';

async function getHandler(request: NextRequest, user: any) {
    return NextResponse.json({
        message: 'هذه نقطة نهاية API محمية للقراءة',
        user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
        },
        timestamp: new Date().toISOString()
    });
}

async function postHandler(request: NextRequest, user: any) {
    const body = await request.json().catch(() => ({}));

    return NextResponse.json({
        message: 'تم إنشاء البيانات بنجاح',
        data: body,
        createdBy: user.name,
        timestamp: new Date().toISOString()
    });
}

// حماية GET مع متطلبات الدور
export const GET = protectApiRoute(getHandler, {
    requiredRole: 'admin',
    rateLimit: RATE_LIMITS.API_GENERAL,
    validateInput: false
});

// حماية POST مع متطلبات الصلاحيات
export const POST = protectApiRoute(postHandler, {
    requiredPermissions: ['CREATE_ITEM'],
    rateLimit: RATE_LIMITS.SENSITIVE_OPS,
    validateInput: true
});
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* نظام ألوان متقدم ومحسن للوضع النهاري */
    /* Advanced and improved color system for light mode */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* نظام ألوان أساسي متدرج */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-50: 221 83% 98%;
    --primary-100: 221 83% 95%;
    --primary-200: 221 83% 88%;
    --primary-300: 221 83% 78%;
    --primary-400: 221 83% 65%;
    --primary-500: 221 83% 53%;
    --primary-600: 221 83% 45%;
    --primary-700: 221 83% 38%;
    --primary-800: 221 83% 32%;
    --primary-900: 221 83% 26%;

    /* ألوان ثانوية محسنة */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    /* أ<PERSON>و<PERSON> محايدة متدرجة */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    /* ألوان الحالة المحسنة */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;

    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* عناصر الواجهة */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;

    /* الظلال والتأثيرات */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
  }

  .dark {
    /* نظام ألوان متقدم ومحسن للوضع الليلي */
    /* Advanced and improved color system for dark mode */
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* نظام ألوان أساسي متدرج للوضع الليلي */
    --primary: 217 91% 60%;
    --primary-foreground: 222 84% 4.9%;
    --primary-50: 217 91% 5%;
    --primary-100: 217 91% 10%;
    --primary-200: 217 91% 20%;
    --primary-300: 217 91% 30%;
    --primary-400: 217 91% 45%;
    --primary-500: 217 91% 60%;
    --primary-600: 217 91% 70%;
    --primary-700: 217 91% 80%;
    --primary-800: 217 91% 88%;
    --primary-900: 217 91% 95%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    /* ألوان الحالة المحسنة للوضع الليلي */
    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;

    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 217 91% 60%;

    /* الظلال والتأثيرات للوضع الليلي */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* استيراد الخطوط العربية والإنجليزية */
/* Import Arabic and English fonts */

/* خطوط عربية محسنة */
/* Improved Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* خطوط إنجليزية محسنة */
/* Improved English fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* تحسينات متقدمة للحركات والتأثيرات */
/* Advanced animations and effects improvements */
@layer utilities {

  /* حركات سلسة ومحسنة */
  /* Smooth and improved animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.4s ease-out;
  }

  .animate-fade-in-down {
    animation: fadeInDown 0.4s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  /* تأثيرات التحويل السلسة */
  /* Smooth transition effects */
  .transition-all-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-colors-smooth {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  }

  .transition-transform-smooth {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-shadow-smooth {
    transition: box-shadow 0.2s ease-in-out;
  }

  /* تأثيرات التفاعل */
  /* Interactive effects */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
  }

  .hover-scale {
    transition: transform 0.2s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  .hover-glow {
    transition: box-shadow 0.2s ease-in-out;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* تأثيرات التركيز المحسنة */
  /* Enhanced focus effects */
  .focus-ring-enhanced {
    transition: box-shadow 0.15s ease-in-out;
  }

  .focus-ring-enhanced:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 0 0 1px rgba(59, 130, 246, 0.2);
  }

  /* تأثيرات التحميل */
  /* Loading effects */
  .loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .dark .loading-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }

  .loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
  }

/* تحسينات للغة العربية والإنجليزية */
/* Improvements for Arabic and English languages */

  /* تحسين عرض النصوص العربية */
  /* Improved Arabic text display */
  .arabic-text {
    font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', 'Almarai', 'IBM Plex Sans Arabic', sans-serif;
    letter-spacing: 0;
    line-height: 1.6;
    font-feature-settings: "kern", "liga", "calt";
  }

  /* تحسين عرض النصوص العربية - خط أنيق */
  /* Improved Arabic text display - elegant font */
  .arabic-text-elegant {
    font-family: 'Tajawal', 'Almarai', 'Cairo', sans-serif;
    letter-spacing: 0;
    line-height: 1.6;
    font-feature-settings: "kern", "liga", "calt";
  }

  /* تحسين عرض النصوص العربية - خط حديث */
  /* Improved Arabic text display - modern font */
  .arabic-text-modern {
    font-family: 'IBM Plex Sans Arabic', 'Noto Sans Arabic', 'Cairo', sans-serif;
    letter-spacing: 0;
    line-height: 1.6;
    font-feature-settings: "kern", "liga", "calt";
  }

  /* تحسين عرض النصوص الإنجليزية */
  /* Improved English text display */
  .english-text {
    font-family: 'Poppins', 'Inter', 'IBM Plex Sans', 'Roboto', system-ui, sans-serif;
    letter-spacing: -0.01em;
    line-height: 1.5;
  }

  /* تحسين عرض النصوص الإنجليزية - خط أنيق */
  /* Improved English text display - elegant font */
  .english-text-elegant {
    font-family: 'Poppins', 'Inter', system-ui, sans-serif;
    letter-spacing: -0.01em;
    line-height: 1.5;
  }

  /* تحسين عرض النصوص الإنجليزية - خط حديث */
  /* Improved English text display - modern font */
  .english-text-modern {
    font-family: 'IBM Plex Sans', 'Roboto', 'Inter', system-ui, sans-serif;
    letter-spacing: -0.01em;
    line-height: 1.5;
  }

  /* تحسين محاذاة العناصر في RTL */
  .rtl-grid {
    direction: rtl;
    text-align: right;
  }

  /* تحسين محاذاة العناصر في LTR */
  .ltr-grid {
    direction: ltr;
    text-align: left;
  }

  /* تحسين الهوامش والحشوات في RTL */
  .rtl-spacing {
    margin-right: 0;
    margin-left: auto;
    padding-right: 0;
    padding-left: 1rem;
  }

  /* تحسين الهوامش والحشوات في LTR */
  .ltr-spacing {
    margin-left: 0;
    margin-right: auto;
    padding-left: 0;
    padding-right: 1rem;
  }

  /* تحسين قراءة النصوص في الوضع الليلي */
  .dark .text-enhance {
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
    font-weight: 400;
  }

  /* تحسين قراءة النصوص في الوضع النهاري */
  .text-enhance {
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.05);
    font-weight: 400;
  }

  /* تحسين عرض الأرقام */
  .tabular-nums {
    font-variant-numeric: tabular-nums;
  }

  /* تحسين عرض العناوين */
  .heading {
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.2;
  }

  /* تحسين عرض الأزرار */
  .button-text {
    font-weight: 500;
    letter-spacing: 0;
  }
}

/* تطبيق الخطوط على كامل التطبيق */
/* Apply fonts to the entire application */
html {
  font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', 'Poppins', 'Inter', system-ui, sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
}

/* تحسين عرض النصوص حسب اللغة */
/* Improve text display based on language */
html[lang="ar"] {
  font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', 'Almarai', 'IBM Plex Sans Arabic', system-ui, sans-serif;
  letter-spacing: 0;
  line-height: 1.6;
}

html[lang="en"] {
  font-family: 'Poppins', 'Inter', 'IBM Plex Sans', 'Roboto', system-ui, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1.5;
}

/* تحسين عرض النصوص في الوضع الليلي */
/* Improve text display in dark mode */
.dark body {
  font-weight: 350;
  /* خط أخف قليلاً في الوضع الليلي لتحسين القراءة */
}

/* تحسين عرض العناوين */
/* Improve headings display */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* تحسين عرض الأزرار */
/* Improve buttons display */
button,
.button {
  font-weight: 500;
  letter-spacing: 0;
}

/* تعريف الحركات المخصصة محسنة للأداء */
/* Custom animations keyframes optimized for performance */
@keyframes fadeIn {
  from {
    opacity: 0;
    will-change: opacity;
  }
  to {
    opacity: 1;
    will-change: auto;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

/* تحسينات الأداء للحركات */
/* Performance optimizations for animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* تقليل الحركات للمستخدمين الذين يفضلون ذلك */
/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Animation delay utilities */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }
.animate-delay-1000 { animation-delay: 1s; }
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { Form, FormField, Input, Textarea, FormActions, FormSection } from '@/components/ui/enhanced-form';

describe('Enhanced Form Components', () => {
  describe('Input', () => {
    it('renders with default props', () => {
      render(<Input placeholder="Enter text" />);
      const input = screen.getByPlaceholderText('Enter text');
      expect(input).toBeInTheDocument();
      expect(input).toHaveClass('border-input');
    });

    it('renders with different variants', () => {
      const { rerender } = render(<Input variant="filled" />);
      expect(screen.getByRole('textbox')).toHaveClass('bg-muted');

      rerender(<Input variant="underlined" />);
      expect(screen.getByRole('textbox')).toHaveClass('border-b-2');
    });

    it('renders with different sizes', () => {
      const { rerender } = render(<Input size="sm" />);
      expect(screen.getByRole('textbox')).toHaveClass('h-9');

      rerender(<Input size="lg" />);
      expect(screen.getByRole('textbox')).toHaveClass('h-12');
    });

    it('shows error state', () => {
      render(<Input error />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('border-destructive');
    });

    it('handles value changes', async () => {
      const user = userEvent.setup();
      const handleChange = jest.fn();
      
      render(<Input onChange={handleChange} />);
      const input = screen.getByRole('textbox');
      
      await user.type(input, 'test value');
      expect(handleChange).toHaveBeenCalled();
    });

    it('supports password toggle', async () => {
      const user = userEvent.setup();
      render(<Input type="password" />);
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'password');
      
      // Look for password toggle button
      const toggleButton = screen.queryByRole('button');
      if (toggleButton) {
        await user.click(toggleButton);
        expect(input).toHaveAttribute('type', 'text');
      }
    });
  });

  describe('Textarea', () => {
    it('renders with default props', () => {
      render(<Textarea placeholder="Enter description" />);
      const textarea = screen.getByPlaceholderText('Enter description');
      expect(textarea).toBeInTheDocument();
      expect(textarea.tagName).toBe('TEXTAREA');
    });

    it('handles value changes', async () => {
      const user = userEvent.setup();
      const handleChange = jest.fn();
      
      render(<Textarea onChange={handleChange} />);
      const textarea = screen.getByRole('textbox');
      
      await user.type(textarea, 'test description');
      expect(handleChange).toHaveBeenCalled();
    });

    it('supports auto-resize', () => {
      render(<Textarea data-auto-resize="true" />);
      const textarea = screen.getByRole('textbox');
      expect(textarea).toHaveAttribute('data-auto-resize', 'true');
    });
  });

  describe('FormField', () => {
    it('renders with label and input', () => {
      render(
        <FormField id="test-field" label="Test Label">
          <Input id="test-field" />
        </FormField>
      );
      
      expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
      expect(screen.getByText('Test Label')).toBeInTheDocument();
    });

    it('shows error message', () => {
      render(
        <FormField id="test-field" label="Test Label" error="This field is required">
          <Input id="test-field" />
        </FormField>
      );
      
      expect(screen.getByText('This field is required')).toBeInTheDocument();
      expect(screen.getByText('This field is required')).toHaveClass('text-destructive');
    });

    it('shows required indicator', () => {
      render(
        <FormField id="test-field" label="Test Label" required>
          <Input id="test-field" />
        </FormField>
      );
      
      expect(screen.getByText('*')).toBeInTheDocument();
    });

    it('shows help text', () => {
      render(
        <FormField id="test-field" label="Test Label" helpText="This is help text">
          <Input id="test-field" />
        </FormField>
      );
      
      expect(screen.getByText('This is help text')).toBeInTheDocument();
    });
  });

  describe('FormActions', () => {
    it('renders action buttons', () => {
      render(
        <FormActions>
          <button type="submit">Submit</button>
          <button type="button">Cancel</button>
        </FormActions>
      );
      
      expect(screen.getByText('Submit')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('applies correct alignment', () => {
      const { rerender } = render(
        <FormActions align="left">
          <button>Button</button>
        </FormActions>
      );
      
      let container = screen.getByText('Button').parentElement;
      expect(container).toHaveClass('justify-start');

      rerender(
        <FormActions align="center">
          <button>Button</button>
        </FormActions>
      );
      
      container = screen.getByText('Button').parentElement;
      expect(container).toHaveClass('justify-center');
    });
  });

  describe('FormSection', () => {
    it('renders with title and description', () => {
      render(
        <FormSection title="Section Title" description="Section description">
          <Input />
        </FormSection>
      );
      
      expect(screen.getByText('Section Title')).toBeInTheDocument();
      expect(screen.getByText('Section description')).toBeInTheDocument();
    });

    it('renders collapsible section', async () => {
      const user = userEvent.setup();
      
      render(
        <FormSection title="Collapsible Section" collapsible>
          <Input data-testid="section-content" />
        </FormSection>
      );
      
      const toggleButton = screen.getByRole('button');
      expect(screen.getByTestId('section-content')).toBeVisible();
      
      await user.click(toggleButton);
      await waitFor(() => {
        expect(screen.queryByTestId('section-content')).not.toBeVisible();
      });
    });
  });

  describe('Form Integration', () => {
    it('handles form submission', async () => {
      const user = userEvent.setup();
      const handleSubmit = jest.fn((e) => e.preventDefault());
      
      render(
        <form onSubmit={handleSubmit}>
          <FormField id="name" label="Name">
            <Input id="name" name="name" />
          </FormField>
          <FormActions>
            <button type="submit">Submit</button>
          </FormActions>
        </form>
      );
      
      const nameInput = screen.getByLabelText('Name');
      await user.type(nameInput, 'John Doe');
      
      const submitButton = screen.getByText('Submit');
      await user.click(submitButton);
      
      expect(handleSubmit).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <FormField id="accessible-field" label="Accessible Field" required>
          <Input id="accessible-field" aria-describedby="help-text" />
        </FormField>
      );
      
      const input = screen.getByLabelText('Accessible Field');
      expect(input).toHaveAttribute('aria-required', 'true');
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <form>
          <FormField id="field1" label="Field 1">
            <Input id="field1" />
          </FormField>
          <FormField id="field2" label="Field 2">
            <Input id="field2" />
          </FormField>
        </form>
      );
      
      const field1 = screen.getByLabelText('Field 1');
      const field2 = screen.getByLabelText('Field 2');
      
      field1.focus();
      expect(field1).toHaveFocus();
      
      await user.tab();
      expect(field2).toHaveFocus();
    });
  });
});
